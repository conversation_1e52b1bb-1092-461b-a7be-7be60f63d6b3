#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix简化客户端

使用直接的zabbix_utils连接，提供可靠的Zabbix API访问。
这是一个实用的解决方案，避免了复杂的MCP通信问题。
"""

import os
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
from utils.logging_config import logger, log_tool_execution

try:
    from zabbix_utils import ZabbixAPI
except ImportError:
    logger.error("zabbix_utils未安装，请运行: uv pip install zabbix_utils")
    raise


class ZabbixSimpleClient:
    """
    Zabbix简化客户端

    直接使用zabbix_utils连接Zabbix，提供可靠的API访问。
    """

    def __init__(self):
        """初始化Zabbix简化客户端"""
        self.api: Optional[ZabbixAPI] = None
        self.timeout = int(os.getenv("ZABBIX_API_TIMEOUT", "30"))
        self.max_retries = int(os.getenv("ZABBIX_API_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("ZABBIX_API_RETRY_DELAY", "1.0"))

        # 加载环境变量（优先使用MCP服务器配置）
        env_file = Path(__file__).parent.parent.parent / "mcp-server" / "zabbix-mcp-server" / "config" / ".env"
        if env_file.exists():
            load_dotenv(env_file, override=True)  # 覆盖已有的环境变量

        # 清除可能冲突的用户名密码环境变量（如果使用Token）
        if os.getenv("ZABBIX_TOKEN"):
            os.environ.pop("ZABBIX_USER", None)
            os.environ.pop("ZABBIX_PASSWORD", None)

        logger.info("初始化Zabbix简化客户端")
        self._connect()

    def _connect(self) -> bool:
        """连接到Zabbix服务器"""
        try:
            url = os.getenv("ZABBIX_URL")
            token = os.getenv("ZABBIX_TOKEN")
            user = os.getenv("ZABBIX_USER")
            password = os.getenv("ZABBIX_PASSWORD")

            if not url:
                raise ValueError("未配置ZABBIX_URL")

            logger.info(f"连接到Zabbix服务器: {url}")

            self.api = ZabbixAPI(url=url)

            # 优先使用Token认证
            if token:
                logger.info("使用API Token认证")
                # 只传递token参数
                self.api.login(token=token)
            elif user and password:
                logger.info(f"使用用户名密码认证: {user}")
                # 只传递用户名密码参数
                self.api.login(user=user, password=password)
            else:
                raise ValueError("未配置认证信息（ZABBIX_TOKEN 或 ZABBIX_USER/ZABBIX_PASSWORD）")

            # 测试连接
            version = self.api.apiinfo.version()
            logger.info(f"Zabbix连接成功，版本: {version}")

            return True

        except Exception as e:
            logger.error(f"连接Zabbix失败: {e}")
            self.api = None
            return False

    def _ensure_connected(self):
        """确保连接有效"""
        if not self.api:
            if not self._connect():
                raise Exception("无法连接到Zabbix服务器")

    @log_tool_execution(tool_name="_call_api")
    def _call_api(self, method: str, params: Dict[str, Any] = None) -> Any:
        """
        调用Zabbix API

        Args:
            method: API方法名（如 'hostgroup.get'）
            params: API参数

        Returns:
            API响应结果
        """
        self._ensure_connected()

        if params is None:
            params = {}

        try:
            # 解析方法名
            if '.' in method:
                object_name, method_name = method.split('.', 1)
                api_object = getattr(self.api, object_name)
                api_method = getattr(api_object, method_name)
            else:
                api_method = getattr(self.api, method)

            # 调用API
            result = api_method(**params)
            return result

        except Exception as e:
            logger.error(f"调用API {method} 失败: {e}")
            raise

    def test_connection(self) -> bool:
        """测试连接"""
        try:
            self._ensure_connected()
            version = self.api.apiinfo.version()
            return True
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    def get_api_version(self) -> str:
        """获取Zabbix API版本"""
        try:
            self._ensure_connected()
            return self.api.apiinfo.version()
        except Exception as e:
            logger.error(f"获取API版本失败: {e}")
            return "Unknown"

    # 以下是Zabbix API工具的封装方法

    def apiinfo_version(self) -> str:
        """获取API版本信息"""
        return self._call_api("apiinfo.version")

    def hostgroup_get(self, **kwargs) -> List[Dict[str, Any]]:
        """获取主机组"""
        return self._call_api("hostgroup.get", kwargs)

    def host_get(self, **kwargs) -> List[Dict[str, Any]]:
        """获取主机"""
        return self._call_api("host.get", kwargs)

    def item_get(self, **kwargs) -> List[Dict[str, Any]]:
        """获取监控项"""
        return self._call_api("item.get", kwargs)

    def history_get(self, **kwargs) -> List[Dict[str, Any]]:
        """获取历史数据"""
        return self._call_api("history.get", kwargs)

    def trend_get(self, **kwargs) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        return self._call_api("trend.get", kwargs)

    def problem_get(self, **kwargs) -> List[Dict[str, Any]]:
        """获取问题"""
        return self._call_api("problem.get", kwargs)

    def event_get(self, **kwargs) -> List[Dict[str, Any]]:
        """获取事件"""
        return self._call_api("event.get", kwargs)

    def trigger_get(self, **kwargs) -> List[Dict[str, Any]]:
        """获取触发器"""
        return self._call_api("trigger.get", kwargs)

    # 添加ZabbixAgent需要的辅助方法

    def get_hostgroup_id(self, hostgroup_name: str) -> Optional[str]:
        """根据主机组名称获取主机组ID"""
        try:
            result = self.hostgroup_get(
                filter={"name": hostgroup_name},
                output=["groupid"]
            )

            if result and len(result) > 0:
                return result[0].get("groupid")

            return None

        except Exception as e:
            logger.error(f"获取主机组ID失败: {e}")
            return None

    def get_hosts_by_groupid(self, groupid: str) -> List[Dict[str, Any]]:
        """根据主机组ID获取主机列表"""
        try:
            result = self.host_get(
                groupids=[groupid],
                output=["hostid", "host", "name", "status"]
            )

            return result if isinstance(result, list) else []

        except Exception as e:
            logger.error(f"获取主机列表失败: {e}")
            return []

    def get_items_by_hostids(self, hostids: List[str], keys: List[str] = None) -> List[Dict[str, Any]]:
        """根据主机ID列表获取监控项"""
        try:
            params = {
                "hostids": hostids,
                "output": ["itemid", "hostid", "key_", "name", "value_type"]
            }

            if keys:
                params["search"] = {"key_": keys}

            result = self.item_get(**params)
            return result if isinstance(result, list) else []

        except Exception as e:
            logger.error(f"获取监控项失败: {e}")
            return []

    def get_history_data(self, itemids: List[str], time_from: int, time_till: int = None) -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            params = {
                "itemids": itemids,
                "time_from": time_from,
                "output": "extend",
                "sortfield": "clock",
                "sortorder": "DESC",
                "limit": 1000
            }

            if time_till:
                params["time_till"] = time_till

            result = self.history_get(**params)
            return result if isinstance(result, list) else []

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []

    def get_host_ids_in_group(self, groupid: str) -> List[str]:
        """根据主机组ID获取主机ID列表"""
        try:
            hosts = self.get_hosts_by_groupid(groupid)
            return [host["hostid"] for host in hosts]
        except Exception as e:
            logger.error(f"获取主机ID列表失败: {e}")
            return []

    def get_item_ids(self, hostids: List[str], keys: List[str]) -> Dict[str, List[str]]:
        """根据主机ID和监控项key获取监控项ID字典"""
        try:
            result = {}
            for key in keys:
                items = self.item_get(
                    hostids=hostids,
                    search={"key_": key},
                    output=["itemid"]
                )
                if items:
                    result[key] = [item["itemid"] for item in items]
            return result
        except Exception as e:
            logger.error(f"获取监控项ID失败: {e}")
            return {}

    def get_history(self, itemids: List[str], time_from: int, time_till: int = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            params = {
                "itemids": itemids,
                "time_from": time_from,
                "output": "extend",
                "sortfield": "clock",
                "sortorder": "DESC",
                "limit": limit
            }

            if time_till:
                params["time_till"] = time_till

            return self.history_get(**params)
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []

    def get_trends(self, itemids: List[str], time_from: int, time_till: int = None) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        try:
            params = {
                "itemids": itemids,
                "time_from": time_from,
                "output": "extend"
            }

            if time_till:
                params["time_till"] = time_till

            return self.trend_get(**params)
        except Exception as e:
            logger.error(f"获取趋势数据失败: {e}")
            return []

    def get_problems(self, groupids: List[str] = None, severities: List[int] = None,
                    time_from: int = None, time_till: int = None, recent: bool = True,
                    limit: int = 100) -> List[Dict[str, Any]]:
        """获取问题数据"""
        try:
            params = {
                "output": "extend",
                "sortfield": "eventid",
                "sortorder": "DESC",
                "limit": limit
            }

            if groupids:
                params["groupids"] = groupids
            if severities:
                params["severities"] = severities
            if time_from:
                params["time_from"] = time_from
            if time_till:
                params["time_till"] = time_till
            if recent:
                params["recent"] = "true"

            return self.problem_get(**params)
        except Exception as e:
            logger.error(f"获取问题数据失败: {e}")
            return []

    def get_triggers(self, groupids: List[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取触发器数据"""
        try:
            params = {
                "output": "extend",
                "limit": limit
            }

            if groupids:
                params["groupids"] = groupids

            return self.trigger_get(**params)
        except Exception as e:
            logger.error(f"获取触发器数据失败: {e}")
            return []