# -*- coding: utf-8 -*-

"""
File: azure_entraid_agent.py
Author: HuangJun
Date: 2025/6/17

Azure EntraID 代理类，提供用户和组管理功能。
"""

import os
import re
import json
import logging
import random
import string
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from langchain.tools import tool
import msal
import requests

from llm.llm import llm
from utils.logging_config import logger, log_tool_execution
from agents.azure.azure_schema import GetUserGroupsSchema, ResetUserPasswordSchema

class EntraIDAgent:
    """
    Azure EntraID 代理类，提供用户和组管理功能。
    """
    
    def __init__(self):
        """
        初始化 EntraID 代理
        """
        self._load_environment_config()
        self._create_msal_app()
        logger.info("EntraID 代理初始化完成")
    
    def _load_environment_config(self):
        """
        加载环境配置
        """
        # 确定使用哪个环境的配置
        environment = os.getenv("ENVIRONMENT", "domestic").lower()
        region = os.getenv("REGION", "CN").upper()
        
        # 根据环境选择配置前缀
        prefix = "DOMESTIC_" if environment == "domestic" else "OVERSEAS_"
        
        # 加载配置
        self.tenant_id = os.getenv(f"{prefix}TENANT_ID")
        self.client_id = os.getenv(f"{prefix}CLIENT_ID")
        self.client_secret = os.getenv(f"{prefix}CLIENT_SECRET")
        self.authority = os.getenv(f"{prefix}AUTHORITY")
        self.graph_endpoint = os.getenv(f"{prefix}GRAPH_ENDPOINT")
        
        # 功能开关
        self.enable_dynamic_group_creation = os.getenv("ENABLE_DYNAMIC_GROUP_CREATION", "true").lower() == "true"
        self.enable_auto_user_assignment = os.getenv("ENABLE_AUTO_USER_ASSIGNMENT", "true").lower() == "true"
        
        # 记录环境信息
        logger.info(f"已加载 {environment.upper()} 环境配置，区域: {region}")
    
    def _create_msal_app(self):
        """
        创建 MSAL 应用
        """
        self.app = msal.ConfidentialClientApplication(
            self.client_id,
            authority=f"{self.authority}/{self.tenant_id}",
            client_credential=self.client_secret
        )
        logger.info("已创建 MSAL 应用")
    
    @log_tool_execution(tool_name="get_access_token")
    def get_access_token(self):
        """
        获取访问令牌
        
        Returns:
            访问令牌
            
        Raises:
            Exception: 获取令牌失败
        """
        scopes = [f"{self.graph_endpoint}/.default"]
        
        # 尝试从缓存获取令牌
        result = self.app.acquire_token_silent(scopes, account=None)
        
        # 如果缓存中没有令牌，则获取新令牌
        if not result:
            result = self.app.acquire_token_for_client(scopes)
        
        # 检查结果
        if "access_token" in result:
            return result["access_token"]
        else:
            error = result.get("error")
            error_description = result.get("error_description")
            raise Exception(f"获取访问令牌失败: {error} - {error_description}")
    
    @log_tool_execution(tool_name="_make_graph_request")
    def _make_graph_request(self, method, endpoint, data=None, params=None):
        """
        发送 Graph API 请求
        
        Args:
            method: 请求方法 (GET, POST, PATCH, DELETE)
            endpoint: API 端点
            data: 请求数据
            params: 查询参数
            
        Returns:
            API 响应
            
        Raises:
            Exception: API 请求失败
        """
        # 获取访问令牌
        access_token = self.get_access_token()
        
        # 构建请求 URL
        url = f"{self.graph_endpoint}/v1.0/{endpoint}"
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        # 发送请求
        try:
            if method == "GET":
                response = requests.get(url, headers=headers, params=params)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=data)
            elif method == "PATCH":
                response = requests.patch(url, headers=headers, json=data)
            elif method == "DELETE":
                response = requests.delete(url, headers=headers)
            else:
                raise ValueError(f"不支持的请求方法: {method}")
            
            # 检查响应状态
            response.raise_for_status()
            
            # 返回响应数据
            if response.status_code == 204:  # No Content
                return {"success": True}
            return response.json()
        except requests.exceptions.RequestException as e:
            error_message = str(e)
            if hasattr(e, "response") and e.response is not None:
                try:
                    error_data = e.response.json()
                    error_message = f"{error_data.get('error', {}).get('message', str(e))}"
                except:
                    pass
            raise Exception(f"Graph API 请求失败: {error_message}")
    
    # 用户管理工具
    @log_tool_execution(tool_name="get_users_tool")
    async def get_users_tool(self):
        """
        获取用户列表工具
        """
        @tool
        async def get_users() -> Dict:
            """
            获取所有用户列表
            
            Returns:
                用户列表
            """
            try:
                result = self._make_graph_request("GET", "users")
                return {"users": result.get("value", [])}
            except Exception as e:
                logger.error(f"获取用户列表失败: {str(e)}", exc_info=True)
                raise
        return get_users
    
    @log_tool_execution(tool_name="get_user_by_id_tool")
    async def get_user_by_id_tool(self):
        """
        根据 ID 获取用户工具
        """
        class GetUserByIdSchema(BaseModel):
            user_id: str = Field(..., description="用户 ID")
        
        @tool(args_schema=GetUserByIdSchema)
        async def get_user_by_id(user_id: str) -> Dict:
            """
            根据 ID 获取用户信息
            
            Args:
                user_id: 用户 ID
                
            Returns:
                用户信息
            """
            try:
                result = self._make_graph_request("GET", f"users/{user_id}")
                return {"user": result}
            except Exception as e:
                logger.error(f"获取用户信息失败: {str(e)}", exc_info=True)
                raise
        return get_user_by_id
    
    @log_tool_execution(tool_name="get_user_by_email_tool")
    async def get_user_by_email_tool(self):
        """
        根据邮箱获取用户工具
        """
        class GetUserByEmailSchema(BaseModel):
            email: str = Field(..., description="用户邮箱")
        
        @tool(args_schema=GetUserByEmailSchema)
        async def get_user_by_email(email: str) -> Dict:
            """
            根据邮箱获取用户信息
            
            Args:
                email: 用户邮箱
                
            Returns:
                用户信息
            """
            try:
                # 使用过滤器查询
                result = self._make_graph_request(
                    "GET", 
                    "users", 
                    params={"$filter": f"mail eq '{email}' or userPrincipalName eq '{email}'"}
                )
                
                users = result.get("value", [])
                if users:
                    return {"user": users[0]}
                else:
                    return {"user": None, "message": f"找不到邮箱为 {email} 的用户"}
            except Exception as e:
                logger.error(f"获取用户信息失败: {str(e)}", exc_info=True)
                raise
        return get_user_by_email
    
    # 组管理工具
    @log_tool_execution(tool_name="get_groups_tool")
    async def get_groups_tool(self):
        """
        获取组列表工具
        """
        @tool
        async def get_groups() -> Dict:
            """
            获取所有组列表
            
            Returns:
                组列表
            """
            try:
                result = self._make_graph_request("GET", "groups")
                return {"groups": result.get("value", [])}
            except Exception as e:
                logger.error(f"获取组列表失败: {str(e)}", exc_info=True)
                raise
        return get_groups
    
    @log_tool_execution(tool_name="get_group_by_name_tool")
    async def get_group_by_name_tool(self):
        """
        根据名称获取组工具
        """
        class GetGroupByNameSchema(BaseModel):
            group_name: str = Field(..., description="组名称")
        
        @tool(args_schema=GetGroupByNameSchema)
        async def get_group_by_name(group_name: str) -> Dict:
            """
            根据名称获取组信息
            
            Args:
                group_name: 组名称
                
            Returns:
                组信息
            """
            try:
                # 使用过滤器查询
                result = self._make_graph_request(
                    "GET", 
                    "groups", 
                    params={"$filter": f"displayName eq '{group_name}'"}
                )
                
                groups = result.get("value", [])
                if groups:
                    return {"group": groups[0]}
                else:
                    return {"group": None, "message": f"找不到名称为 {group_name} 的组"}
            except Exception as e:
                logger.error(f"获取组信息失败: {str(e)}", exc_info=True)
                raise
        return get_group_by_name
    
    @log_tool_execution(tool_name="get_group_members_tool")
    async def get_group_members_tool(self):
        """
        获取组成员工具
        """
        class GetGroupMembersSchema(BaseModel):
            group_id: str = Field(..., description="组 ID")
        
        @tool(args_schema=GetGroupMembersSchema)
        async def get_group_members(group_id: str) -> Dict:
            """
            获取组成员列表
            
            Args:
                group_id: 组 ID
                
            Returns:
                成员列表
            """
            try:
                result = self._make_graph_request("GET", f"groups/{group_id}/members")
                return {"members": result.get("value", [])}
            except Exception as e:
                logger.error(f"获取组成员失败: {str(e)}", exc_info=True)
                raise
        return get_group_members
    
    # 环境信息工具
    @log_tool_execution(tool_name="get_environment_info_tool")
    async def get_environment_info_tool(self):
        """
        获取环境信息工具
        """
        @tool
        async def get_environment_info() -> Dict:
            """
            获取当前环境信息
            
            Returns:
                环境信息
            """
            environment = os.getenv("ENVIRONMENT", "domestic").lower()
            region = os.getenv("REGION", "CN").upper()
            
            return {
                "environment": environment,
                "region": region,
                "tenant_id": self.tenant_id,
                "enable_dynamic_group_creation": self.enable_dynamic_group_creation,
                "enable_auto_user_assignment": self.enable_auto_user_assignment
            }
        return get_environment_info
    
    # 用户管理工具
    @log_tool_execution(tool_name="add_user_tool")
    async def add_user_tool(self):
        """
        添加用户工具
        """
        class AddUserSchema(BaseModel):
            display_name: str = Field(..., description="用户显示名称")
            user_principal_name: str = Field(..., description="用户主体名称（通常是邮箱）")
            email: Optional[str] = Field(None, description="用户电子邮件地址（如果不提供，将使用user_principal_name）")
            mail_nickname: Optional[str] = Field(None, description="邮箱昵称（如果不提供，将从主体名称生成）")
            department: Optional[str] = Field(None, description="部门")
            job_title: Optional[str] = Field(None, description="职位")
            mobile_phone: Optional[str] = Field(None, description="手机号码")
            password: Optional[str] = Field(None, description="初始密码（如果不提供，将生成随机密码）")
        
        @tool(args_schema=AddUserSchema)
        async def add_user(
            display_name: str,
            user_principal_name: str,
            email: Optional[str] = None,
            mail_nickname: Optional[str] = None,
            department: Optional[str] = None,
            job_title: Optional[str] = None,
            mobile_phone: Optional[str] = None,
            password: Optional[str] = None
        ) -> Dict:
            """
            添加新用户
            
            Args:
                display_name: 用户显示名称
                user_principal_name: 用户主体名称（通常是邮箱）
                email: 用户电子邮件地址（如果不提供，将使用user_principal_name）
                mail_nickname: 邮箱昵称（如果不提供，将从主体名称生成）
                department: 部门
                job_title: 职位
                mobile_phone: 手机号码
                password: 初始密码（如果不提供，将生成随机密码）
                
            Returns:
                新用户信息
            """
            try:
                # 拆分显示名称为姓和名
                # 检查是否为中文名称（没有空格分隔）
                if len(display_name.split()) == 1 and any('\u4e00' <= char <= '\u9fff' for char in display_name):
                    # 中文名称处理：第一个字作为姓，其余作为名
                    surname = display_name[0:1]  # 取第一个字作为姓
                    given_name = display_name[1:] if len(display_name) > 1 else display_name  # 剩余部分作为名
                else:
                    # 非中文名称或有空格的名称按原逻辑处理
                    name_parts = display_name.split()
                    given_name = name_parts[-1] if len(name_parts) > 0 else display_name
                    surname = " ".join(name_parts[:-1]) if len(name_parts) > 1 else ""
                
                # 确保 surname 不为空，Microsoft Graph API 要求 surname 必须有值
                if not surname:
                    surname = "未知"  # 设置默认姓氏
                
                # 如果没有提供邮箱昵称，从主体名称生成
                if not mail_nickname:
                    mail_nickname = user_principal_name.split("@")[0]
                
                # 如果没有提供密码，使用默认密码
                if not password:
                    password = "InHand@2022@better"
                
                # 准备用户数据
                user_data = {
                    "accountEnabled": True,
                    "displayName": display_name,
                    "mailNickname": mail_nickname,
                    "userPrincipalName": user_principal_name,
                    "givenName": given_name,
                    "surname": surname,
                    "passwordProfile": {
                        "forceChangePasswordNextSignIn": True,
                        "password": password
                    }
                }
                
                # 添加可选字段
                if email:
                    user_data["mail"] = email
                if department:
                    user_data["department"] = department
                if job_title:
                    user_data["jobTitle"] = job_title
                if mobile_phone:
                    user_data["mobilePhone"] = mobile_phone
                
                # 创建用户
                result = self._make_graph_request("POST", "users", data=user_data)
                
                # 在返回结果中包含密码信息
                return {"user": result, "password": password, "message": f"已成功创建用户 {display_name}，初始密码为: {password}"}
            except Exception as e:
                logger.error(f"创建用户失败: {str(e)}", exc_info=True)
                raise
        return add_user
    
    @log_tool_execution(tool_name="delete_user_tool")
    async def delete_user_tool(self):
        """
        删除用户工具
        """
        class DeleteUserSchema(BaseModel):
            user_identifier: str = Field(..., description="用户标识符（ID 或邮箱）")
        
        @tool(args_schema=DeleteUserSchema)
        async def delete_user(user_identifier: str) -> Dict:
            """
            删除用户
            
            Args:
                user_identifier: 用户标识符（ID 或邮箱）
                
            Returns:
                操作结果
            """
            try:
                # 判断标识符类型
                if "@" in user_identifier:  # 邮箱
                    # 先查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_identifier}' or userPrincipalName eq '{user_identifier}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_identifier} 的用户"}
                    
                    user_id = users[0]["id"]
                else:  # ID
                    user_id = user_identifier
                
                # 删除用户
                self._make_graph_request("DELETE", f"users/{user_id}")
                
                return {"success": True, "message": f"已成功删除用户 {user_identifier}"}
            except Exception as e:
                logger.error(f"删除用户失败: {str(e)}", exc_info=True)
                raise
        return delete_user
    
    @log_tool_execution(tool_name="add_group_tool")
    async def add_group_tool(self):
        """
        添加组工具
        """
        class AddGroupSchema(BaseModel):
            display_name: str = Field(..., description="组显示名称")
            description: Optional[str] = Field(None, description="组描述")
            mail_nickname: Optional[str] = Field(None, description="邮箱昵称（如果不提供，将从显示名称生成）")
            mail_enabled: Optional[bool] = Field(False, description="是否启用邮箱")
            security_enabled: Optional[bool] = Field(True, description="是否启用安全组")
        
        @tool(args_schema=AddGroupSchema)
        async def add_group(
            display_name: str,
            description: Optional[str] = None,
            mail_nickname: Optional[str] = None,
            mail_enabled: Optional[bool] = False,
            security_enabled: Optional[bool] = True
        ) -> Dict:
            """
            添加新组
            
            Args:
                display_name: 组显示名称
                description: 组描述
                mail_nickname: 邮箱昵称（如果不提供，将从显示名称生成）
                mail_enabled: 是否启用邮箱
                security_enabled: 是否启用安全组
                
            Returns:
                新组信息
            """
            try:
                # 检查组是否已存在
                existing_group = self._make_graph_request(
                    "GET", 
                    "groups", 
                    params={"$filter": f"displayName eq '{display_name}'"}
                )
                
                if existing_group.get("value") and len(existing_group["value"]) > 0:
                    return {"success": False, "message": f"组 {display_name} 已存在", "group": existing_group["value"][0]}
                
                # 如果没有提供邮箱昵称，从显示名称生成
                if not mail_nickname:
                    # 移除特殊字符，用下划线替换空格
                    mail_nickname = re.sub(r'[^\w\s]', '', display_name).replace(' ', '_')
                
                # 准备组数据
                group_data = {
                    "displayName": display_name,
                    "mailNickname": mail_nickname,
                    "mailEnabled": mail_enabled,
                    "securityEnabled": security_enabled
                }
                
                # 添加可选字段
                if description:
                    group_data["description"] = description
                
                # 创建组
                result = self._make_graph_request("POST", "groups", data=group_data)
                
                return {"group": result, "message": f"已成功创建组 {display_name}"}
            except Exception as e:
                logger.error(f"创建组失败: {str(e)}", exc_info=True)
                raise
        return add_group
    
    @log_tool_execution(tool_name="delete_group_tool")
    async def delete_group_tool(self):
        """
        删除组工具
        """
        class DeleteGroupSchema(BaseModel):
            group_identifier: str = Field(..., description="组标识符（ID 或名称）")
        
        @tool(args_schema=DeleteGroupSchema)
        async def delete_group(group_identifier: str) -> Dict:
            """
            删除组
            
            Args:
                group_identifier: 组标识符（ID 或名称）
                
            Returns:
                操作结果
            """
            try:
                # 判断标识符类型
                if not re.match(r'^[0-9a-f-]+$', group_identifier):  # 不是 ID 格式
                    # 先查找组
                    result = self._make_graph_request(
                        "GET", 
                        "groups", 
                        params={"$filter": f"displayName eq '{group_identifier}'"}
                    )
                    
                    groups = result.get("value", [])
                    if not groups:
                        return {"success": False, "message": f"找不到名称为 {group_identifier} 的组"}
                    
                    group_id = groups[0]["id"]
                else:  # ID
                    group_id = group_identifier
                
                # 删除组
                self._make_graph_request("DELETE", f"groups/{group_id}")
                
                return {"success": True, "message": f"已成功删除组 {group_identifier}"}
            except Exception as e:
                logger.error(f"删除组失败: {str(e)}", exc_info=True)
                raise
        return delete_group
    
    @log_tool_execution(tool_name="add_user_to_group_tool")
    async def add_user_to_group_tool(self):
        """
        将用户添加到组工具
        """
        class AddUserToGroupSchema(BaseModel):
            user_identifier: str = Field(..., description="用户标识符（ID 或邮箱）")
            group_identifier: str = Field(..., description="组标识符（ID 或名称）")
        
        @tool(args_schema=AddUserToGroupSchema)
        async def add_user_to_group(user_identifier: str, group_identifier: str) -> Dict:
            """
            将用户添加到组
            
            Args:
                user_identifier: 用户标识符（ID 或邮箱）
                group_identifier: 组标识符（ID 或名称）
                
            Returns:
                操作结果
            """
            try:
                # 获取用户 ID
                user_id = user_identifier
                if "@" in user_identifier:  # 邮箱
                    # 查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_identifier}' or userPrincipalName eq '{user_identifier}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_identifier} 的用户"}
                    
                    user_id = users[0]["id"]
                
                # 获取组 ID
                group_id = group_identifier
                if not re.match(r'^[0-9a-f-]+$', group_identifier):  # 不是 ID 格式
                    # 查找组
                    result = self._make_graph_request(
                        "GET", 
                        "groups", 
                        params={"$filter": f"displayName eq '{group_identifier}'"}
                    )
                    
                    groups = result.get("value", [])
                    if not groups:
                        return {"success": False, "message": f"找不到名称为 {group_identifier} 的组"}
                    
                    group_id = groups[0]["id"]
                
                # 添加成员
                self._make_graph_request(
                    "POST", 
                    f"groups/{group_id}/members/$ref", 
                    data={
                        "@odata.id": f"{self.graph_endpoint}/v1.0/directoryObjects/{user_id}"
                    }
                )
                
                return {"success": True, "message": f"已成功将用户 {user_identifier} 添加到组 {group_identifier}"}
            except Exception as e:
                logger.error(f"添加用户到组失败: {str(e)}", exc_info=True)
                raise
        return add_user_to_group
    
    @log_tool_execution(tool_name="remove_user_from_group_tool")
    async def remove_user_from_group_tool(self):
        """
        从组中移除用户工具
        """
        class RemoveUserFromGroupSchema(BaseModel):
            user_identifier: str = Field(..., description="用户标识符（ID 或邮箱）")
            group_identifier: str = Field(..., description="组标识符（ID 或名称）")
        
        @tool(args_schema=RemoveUserFromGroupSchema)
        async def remove_user_from_group(user_identifier: str, group_identifier: str) -> Dict:
            """
            从组中移除用户
            
            Args:
                user_identifier: 用户标识符（ID 或邮箱）
                group_identifier: 组标识符（ID 或名称）
                
            Returns:
                操作结果
            """
            try:
                # 获取用户 ID
                user_id = user_identifier
                if "@" in user_identifier:  # 邮箱
                    # 查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_identifier}' or userPrincipalName eq '{user_identifier}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_identifier} 的用户"}
                    
                    user_id = users[0]["id"]
                
                # 获取组 ID
                group_id = group_identifier
                if not re.match(r'^[0-9a-f-]+$', group_identifier):  # 不是 ID 格式
                    # 查找组
                    result = self._make_graph_request(
                        "GET", 
                        "groups", 
                        params={"$filter": f"displayName eq '{group_identifier}'"}
                    )
                    
                    groups = result.get("value", [])
                    if not groups:
                        return {"success": False, "message": f"找不到名称为 {group_identifier} 的组"}
                    
                    group_id = groups[0]["id"]
                
                # 移除成员
                self._make_graph_request("DELETE", f"groups/{group_id}/members/{user_id}/$ref")
                
                return {"success": True, "message": f"已成功从组 {group_identifier} 中移除用户 {user_identifier}"}
            except Exception as e:
                logger.error(f"从组中移除用户失败: {str(e)}", exc_info=True)
                raise
        return remove_user_from_group
    
    @log_tool_execution(tool_name="update_user_tool")
    async def update_user_tool(self):
        """
        更新用户信息工具
        """
        class UpdateUserSchema(BaseModel):
            user_identifier: str = Field(..., description="用户标识符（ID 或邮箱）")
            display_name: Optional[str] = Field(None, description="用户显示名称")
            department: Optional[str] = Field(None, description="部门")
            job_title: Optional[str] = Field(None, description="职位")
            mobile_phone: Optional[str] = Field(None, description="手机号码")
            account_enabled: Optional[bool] = Field(None, description="账号是否启用")
        
        @tool(args_schema=UpdateUserSchema)
        async def update_user(
            user_identifier: str,
            display_name: Optional[str] = None,
            department: Optional[str] = None,
            job_title: Optional[str] = None,
            mobile_phone: Optional[str] = None,
            account_enabled: Optional[bool] = None
        ) -> Dict:
            """
            更新用户信息
            
            Args:
                user_identifier: 用户标识符（ID 或邮箱）
                display_name: 用户显示名称
                department: 部门
                job_title: 职位
                mobile_phone: 手机号码
                account_enabled: 账号是否启用
                
            Returns:
                更新后的用户信息
            """
            try:
                # 获取用户 ID
                user_id = user_identifier
                if "@" in user_identifier:  # 邮箱
                    # 查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_identifier}' or userPrincipalName eq '{user_identifier}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_identifier} 的用户"}
                    
                    user_id = users[0]["id"]
                
                # 准备更新数据
                update_data = {}
                
                if display_name is not None:
                    update_data["displayName"] = display_name
                    
                    # 拆分显示名称为姓和名
                    name_parts = display_name.split()
                    given_name = name_parts[-1] if len(name_parts) > 0 else display_name
                    surname = " ".join(name_parts[:-1]) if len(name_parts) > 1 else ""
                    
                    update_data["givenName"] = given_name
                    update_data["surname"] = surname
                
                if department is not None:
                    update_data["department"] = department
                
                if job_title is not None:
                    update_data["jobTitle"] = job_title
                
                if mobile_phone is not None:
                    update_data["mobilePhone"] = mobile_phone
                
                if account_enabled is not None:
                    update_data["accountEnabled"] = account_enabled
                
                # 如果没有要更新的数据，返回错误
                if not update_data:
                    return {"success": False, "message": "没有提供要更新的数据"}
                
                # 更新用户
                result = self._make_graph_request("PATCH", f"users/{user_id}", data=update_data)
                
                # 获取更新后的用户信息
                updated_user = self._make_graph_request("GET", f"users/{user_id}")
                
                return {"success": True, "message": f"已成功更新用户 {user_identifier} 的信息", "user": updated_user}
            except Exception as e:
                logger.error(f"更新用户信息失败: {str(e)}", exc_info=True)
                raise
        return update_user
    
    @log_tool_execution(tool_name="check_user_in_group_tool")
    async def check_user_in_group_tool(self):
        """
        检查用户是否在组中工具
        """
        class CheckUserInGroupSchema(BaseModel):
            user_identifier: str = Field(..., description="用户标识符（ID 或邮箱）")
            group_identifier: str = Field(..., description="组标识符（ID 或名称）")
        
        @tool(args_schema=CheckUserInGroupSchema)
        async def check_user_in_group(user_identifier: str, group_identifier: str) -> Dict:
            """
            检查用户是否在组中
            
            Args:
                user_identifier: 用户标识符（ID 或邮箱）
                group_identifier: 组标识符（ID 或名称）
                
            Returns:
                检查结果
            """
            try:
                # 获取用户 ID
                user_id = user_identifier
                if "@" in user_identifier:  # 邮箱
                    # 查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_identifier}' or userPrincipalName eq '{user_identifier}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_identifier} 的用户"}
                    
                    user_id = users[0]["id"]
                
                # 获取组 ID
                group_id = group_identifier
                if not re.match(r'^[0-9a-f-]+$', group_identifier):  # 不是 ID 格式
                    # 查找组
                    result = self._make_graph_request(
                        "GET", 
                        "groups", 
                        params={"$filter": f"displayName eq '{group_identifier}'"}
                    )
                    
                    groups = result.get("value", [])
                    if not groups:
                        return {"success": False, "message": f"找不到名称为 {group_identifier} 的组"}
                    
                    group_id = groups[0]["id"]
                
                # 检查成员关系
                try:
                    self._make_graph_request("GET", f"groups/{group_id}/members/{user_id}/$ref")
                    return {"is_member": True, "message": f"用户 {user_identifier} 是组 {group_identifier} 的成员"}
                except Exception:
                    return {"is_member": False, "message": f"用户 {user_identifier} 不是组 {group_identifier} 的成员"}
            except Exception as e:
                logger.error(f"检查用户组成员关系失败: {str(e)}", exc_info=True)
                raise
        return check_user_in_group
    
    @log_tool_execution(tool_name="enable_user_tool")
    async def enable_user_tool(self):
        """
        启用用户账号工具
        """
        class EnableUserSchema(BaseModel):
            user_identifier: str = Field(..., description="用户标识符（ID 或邮箱）")
        
        @tool(args_schema=EnableUserSchema)
        async def enable_user(user_identifier: str) -> Dict:
            """
            启用用户账号
            
            Args:
                user_identifier: 用户标识符（ID 或邮箱）
                
            Returns:
                操作结果
            """
            try:
                # 获取用户 ID
                user_id = user_identifier
                if "@" in user_identifier:  # 邮箱
                    # 查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_identifier}' or userPrincipalName eq '{user_identifier}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_identifier} 的用户"}
                    
                    user_id = users[0]["id"]
                    
                    # 检查用户是否已启用
                    if users[0].get("accountEnabled", False):
                        return {"success": True, "message": f"用户 {user_identifier} 已经处于启用状态"}
                
                # 启用用户
                self._make_graph_request("PATCH", f"users/{user_id}", data={"accountEnabled": True})
                
                return {"success": True, "message": f"已成功启用用户 {user_identifier}"}
            except Exception as e:
                logger.error(f"启用用户失败: {str(e)}", exc_info=True)
                raise
        return enable_user
    
    @log_tool_execution(tool_name="disable_user_tool")
    async def disable_user_tool(self):
        """
        禁用用户账号工具
        """
        class DisableUserSchema(BaseModel):
            user_identifier: str = Field(..., description="用户标识符（ID 或邮箱）")
        
        @tool(args_schema=DisableUserSchema)
        async def disable_user(user_identifier: str) -> Dict:
            """
            禁用用户账号
            
            Args:
                user_identifier: 用户标识符（ID 或邮箱）
                
            Returns:
                操作结果
            """
            try:
                # 获取用户 ID
                user_id = user_identifier
                if "@" in user_identifier:  # 邮箱
                    # 查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_identifier}' or userPrincipalName eq '{user_identifier}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_identifier} 的用户"}
                    
                    user_id = users[0]["id"]
                    
                    # 检查用户是否已禁用
                    if not users[0].get("accountEnabled", True):
                        return {"success": True, "message": f"用户 {user_identifier} 已经处于禁用状态"}
                
                # 禁用用户
                self._make_graph_request("PATCH", f"users/{user_id}", data={"accountEnabled": False})
                
                return {"success": True, "message": f"已成功禁用用户 {user_identifier}"}
            except Exception as e:
                logger.error(f"禁用用户失败: {str(e)}", exc_info=True)
                raise
        return disable_user
    

    
    @log_tool_execution(tool_name="get_user_groups_tool")
    async def get_user_groups_tool(self):
        """
        获取用户所在的所有组工具
        """
        @tool(args_schema=GetUserGroupsSchema)
        async def get_user_groups(user_id_or_email: str) -> Dict:
            """
            获取用户所在的所有组
            
            Args:
                user_id_or_email: 用户ID或电子邮件地址
                
            Returns:
                用户所在的组列表
            """
            try:
                # 获取用户 ID
                user_id = user_id_or_email
                if "@" in user_id_or_email:  # 邮箱
                    # 查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_id_or_email}' or userPrincipalName eq '{user_id_or_email}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_id_or_email} 的用户"}
                    
                    user_id = users[0]["id"]
                
                # 获取用户所在的组
                result = self._make_graph_request(
                    "GET",
                    f"users/{user_id}/memberOf",
                    params={"$select": "displayName,id"}
                )
                
                groups = result.get("value", [])
                group_list = [{"id": group["id"], "name": group["displayName"]} for group in groups]
                
                return {"success": True, "groups": group_list, "message": f"已成功获取用户 {user_id_or_email} 所在的所有组"}
            except Exception as e:
                logger.error(f"获取用户所在组失败: {str(e)}", exc_info=True)
                return {"success": False, "message": f"获取用户所在组失败: {str(e)}"}
        return get_user_groups
    
    @log_tool_execution(tool_name="get_disabled_users_tool")
    async def get_disabled_users_tool(self):
        """
        获取已禁用用户列表工具
        """
        @tool
        async def get_disabled_users() -> Dict:
            """
            获取所有已禁用的用户列表
            
            Returns:
                已禁用用户列表
            """
            try:
                result = self._make_graph_request(
                    "GET", 
                    "users", 
                    params={"$filter": "accountEnabled eq false"}
                )
                return {"users": result.get("value", [])}
            except Exception as e:
                logger.error(f"获取已禁用用户列表失败: {str(e)}", exc_info=True)
                raise
        return get_disabled_users
    
    @log_tool_execution(tool_name="reset_user_password_tool")
    async def reset_user_password_tool(self):
        """
        重置用户密码工具
        """
        @tool(args_schema=ResetUserPasswordSchema)
        async def reset_user_password(user_identifier: str, new_password: Optional[str] = None) -> Dict:
            """
            重置用户密码
            
            Args:
                user_identifier: 用户标识符（ID 或邮箱）
                new_password: 新密码（如果不提供，将使用默认密码）
                
            Returns:
                操作结果
            """
            try:
                # 获取用户 ID
                user_id = user_identifier
                if "@" in user_identifier:  # 邮箱
                    # 查找用户
                    result = self._make_graph_request(
                        "GET", 
                        "users", 
                        params={"$filter": f"mail eq '{user_identifier}' or userPrincipalName eq '{user_identifier}'"}
                    )
                    
                    users = result.get("value", [])
                    if not users:
                        return {"success": False, "message": f"找不到邮箱为 {user_identifier} 的用户"}
                    
                    user_id = users[0]["id"]
                
                # 如果没有提供密码，使用默认密码
                if not new_password:
                    new_password = "InHand@2022@better"
                
                # 重置密码
                self._make_graph_request(
                    "PATCH", 
                    f"users/{user_id}", 
                    data={
                        "passwordProfile": {
                            "forceChangePasswordNextSignIn": True,
                            "password": new_password
                        }
                    }
                )
                
                return {"success": True, "message": f"已成功重置用户 {user_identifier} 的密码", "password": new_password}
            except Exception as e:
                logger.error(f"重置用户密码失败: {str(e)}", exc_info=True)
                raise
        return reset_user_password
    
    @log_tool_execution(tool_name="get_tools")
    async def get_tools(self) -> List:
        """
        获取所有可用工具的列表
        
        Returns:
            工具列表
        """
        tools = [
            await self.get_users_tool(),
            await self.get_user_by_id_tool(),
            await self.get_user_by_email_tool(),
            await self.get_groups_tool(),
            await self.get_group_by_name_tool(),
            await self.get_group_members_tool(),
            await self.get_environment_info_tool(),
            await self.add_user_tool(),
            await self.delete_user_tool(),
            await self.add_group_tool(),
            await self.delete_group_tool(),
            await self.add_user_to_group_tool(),
            await self.remove_user_from_group_tool(),
            await self.update_user_tool(),
            await self.check_user_in_group_tool(),
            await self.enable_user_tool(),
            await self.disable_user_tool(),
            await self.get_user_groups_tool(),
            await self.get_disabled_users_tool(),
            await self.reset_user_password_tool(),
        ]
        logger.info(f"已加载工具列表: {[tool.name for tool in tools]}")
        return tools
    
    @log_tool_execution(tool_name="invoke")
    async def invoke(self, task: str) -> Any:
        """
        使用 LangChain 的 agent_executor 模式调用工具
        
        Args:
            task: 用户任务描述
        
        Returns:
            agent_executor 的执行结果
            
        Raises:
            Exception: 调用失败
        """
        class ResultModel(BaseModel):
            task: str = Field(..., description="任务执行内容")
            status: str = Field(..., description="操作结果状态，success | failed")
            result: str = Field(..., description="执行完任务后返回的结果，不更改工具返回的结果描述")
            failed_reason: Optional[str] = Field(None, description="执行失败时的原因，详细描述，如执行了什么操作，返回了什么结果")
        
        logger.info(f"开始处理任务: {task}")
        tools = await self.get_tools()
        
        # 从azure_prompt.md文件中读取系统提示词
        prompt_file_path = os.path.join(os.path.dirname(__file__), 'azure_prompt.md')
        with open(prompt_file_path, 'r', encoding='utf-8') as f:
            system_prompt = f.read().strip()
        
        # 创建 agent_executor
        logger.info("创建 agent_executor")
        agent_executor = create_react_agent(
            llm.bind_tools(tools, parallel_tool_calls=False),
            tools=tools,
            response_format=ResultModel
        )
        
        # 执行 agent
        logger.info("开始执行 agent")
        try:
            response = await agent_executor.ainvoke(
                input={
                    "messages": [
                        SystemMessage(system_prompt),
                        HumanMessage(task),
                    ]
                },
                config=RunnableConfig(
                    recursion_limit=100,
                ),
            )
            
            structured_response = response.get("structured_response")
            logger.info(f"任务执行完成，状态: {structured_response.status}")
            return structured_response
        except Exception as e:
            logger.error(f"执行agent时发生错误: {str(e)}", exc_info=True)
            # 创建一个错误响应
            error_response = ResultModel(
                task=task,
                status="failed",
                result="任务执行失败",
                failed_reason=f"执行过程中发生错误: {str(e)}"
            )
            return error_response




