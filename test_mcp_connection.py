#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试MCP连接
"""

import sys
import asyncio
sys.path.append('agents/zabbix')

async def test_mcp_connection():
    """测试MCP连接"""
    try:
        from zabbix_mcp_simple import ZabbixMCPSimple

        print('🔧 创建ZabbixMCPSimple...')
        client = ZabbixMCPSimple(auto_start_server=True)

        print('🔍 测试连接（30秒超时）...')
        if await client.connect():
            print('✅ MCP连接成功')

            print('📊 测试API版本...')
            try:
                result = await client.call_tool("apiinfo_version")
                print(f'✅ API版本: {result}')
            except Exception as e:
                print(f'⚠️  API版本获取失败: {e}')

            print('🏠 测试获取主机组...')
            try:
                result = await client.call_tool("hostgroup_get", {
                    "output": ["groupid", "name"],
                    "limit": 3
                })
                print(f'✅ 获取到{len(result)}个主机组')
                for group in result[:3]:  # 只显示前3个
                    print(f'   - {group["name"]} (ID: {group["groupid"]})')
            except Exception as e:
                print(f'⚠️  主机组获取失败: {e}')

            await client.disconnect()
            print('✅ 断开连接成功')

        else:
            print('❌ MCP连接失败')

    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mcp_connection())