# -*- coding: utf-8 -*-

"""
LetsEncrypt SSL证书管理代理（基于certbot）

基于certbot命令行工具的SSL证书自动化管理代理。
支持AWS Route53和阿里云DNS验证，提供完整的证书生命周期管理。

主要功能：
- 证书签发：单域名、多域名、通配符证书
- DNS验证：AWS Route53、阿里云DNS自动化验证
- 证书管理：状态查询、文件管理、自动复制
- 错误处理：智能重试、详细日志、问题诊断

技术特点：
- 基于certbot官方工具，稳定可靠
- 支持多种密钥类型（RSA、ECDSA）
- 支持证书链选择
- 跨平台兼容（Windows、Linux）

Author: AI Assistant & Team
Date: 2025/6/20
"""

import os
import sys
import logging
import ssl
import socket
import subprocess
from typing import Dict, Optional, List, Any

from pydantic import BaseModel, Field
from dotenv import load_dotenv
from langchain_core.tools import tool
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent

from llm.llm import llm
from utils.logging_config import log_tool_execution
from agents.letsencrypt.letsencrypt_schema import (
    IssueCertificateSchema,
    CheckCertificateStatusSchema,
)

# 配置日志
logger = logging.getLogger(__name__)


class FixedLetsEncryptAgent:
    """修复版LetsEncrypt SSL证书管理代理
    
    使用certbot命令行工具进行证书管理，避免复杂的ACME库问题。
    """
    
    def __init__(self):
        """初始化修复版LetsEncrypt证书管理代理"""
        load_dotenv()
        
        # 加载配置
        self.config = self._load_config()
        
        # 验证必要的环境变量和配置
        self._validate_config()
        
        # 初始化证书存储
        self._init_certificate_storage()

        # 加载平台域名文档
        self.platform_docs = self._load_platform_docs()

        logger.info("修复版LetsEncrypt代理初始化完成")

    @log_tool_execution()
    def _load_config(self) -> Dict[str, Any]:
        """加载配置信息"""
        return {
            # 证书存储路径
            'cert_base_path': os.getenv('CERT_BASE_PATH', './letsencrypt'),
            'cert_output_path': os.getenv('CERT_OUTPUT_PATH', './data/certificates'),
            
            # DNS提供商配置
            'aws_access_key_id': os.getenv('AWS_ACCESS_KEY_ID'),
            'aws_secret_access_key': os.getenv('AWS_SECRET_ACCESS_KEY'),
            'aws_region': os.getenv('AWS_REGION', 'us-east-1'),
            'alicloud_access_key': os.getenv('ALICLOUD_ACCESS_KEY'),
            'alicloud_secret_key': os.getenv('ALICLOUD_SECRET_KEY'),

            # Cloudflare配置
            'cloudflare_email': os.getenv('CLOUDFLARE_EMAIL'),
            'cloudflare_api_key': os.getenv('CLOUDFLARE_API_KEY'),
            'cloudflare_api_token': os.getenv('CLOUDFLARE_API_TOKEN'),
            'alicloud_region': os.getenv('ALICLOUD_REGION', 'cn-hangzhou'),
            
            # Let's Encrypt配置
            'default_email': os.getenv('LETSENCRYPT_EMAIL', '<EMAIL>'),
            'staging': os.getenv('LETSENCRYPT_STAGING', 'false').lower() == 'true',
        }

    @log_tool_execution()
    def _validate_config(self) -> None:
        """验证配置是否完整"""
        # 检查certbot是否可用
        try:
            result = subprocess.run(['certbot', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"Certbot可用: {result.stdout.strip()}")
            else:
                logger.warning("Certbot不可用，将使用简化功能")
        except FileNotFoundError:
            logger.warning("Certbot未安装，将使用简化功能")
        
        # 检查DNS提供商配置
        dns_providers = []

        if self.config['aws_access_key_id'] and self.config['aws_secret_access_key']:
            dns_providers.append("AWS Route53")

        if self.config['alicloud_access_key'] and self.config['alicloud_secret_key']:
            dns_providers.append("阿里云DNS")

        # 检查Cloudflare DNS配置
        cloudflare_email = self.config.get('cloudflare_email')
        cloudflare_api_key = self.config.get('cloudflare_api_key')
        cloudflare_api_token = self.config.get('cloudflare_api_token')

        if cloudflare_email and (cloudflare_api_key or cloudflare_api_token):
            dns_providers.append("Cloudflare DNS")

        if dns_providers:
            logger.info(f"已配置DNS提供商: {', '.join(dns_providers)}")
        else:
            logger.warning("未配置任何DNS提供商，请在.env文件中配置相关API密钥")

    @log_tool_execution()
    def _init_certificate_storage(self) -> None:
        """初始化证书存储目录"""
        # 确保data目录存在
        cert_output_path = self.config['cert_output_path']
        data_dir = os.path.dirname(cert_output_path)  # ./data
        if data_dir and data_dir != '.':
            os.makedirs(data_dir, exist_ok=True)
            logger.info(f"数据目录初始化完成: {data_dir}")

        directories = [
            self.config['cert_base_path'],
            self.config['cert_output_path'],
            os.path.join(self.config['cert_base_path'], 'live'),
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        logger.info(f"证书存储初始化完成，基础路径: {self.config['cert_base_path']}")
        logger.info(f"证书输出路径: {self.config['cert_output_path']}")

    @log_tool_execution()
    def _load_platform_docs(self) -> str:
        """加载平台域名数据（从API获取）"""
        try:
            from .certificate_api_client import CertificateAPIClient

            # 创建API客户端
            api_client = CertificateAPIClient()

            # 获取平台映射数据
            platform_mapping = api_client.build_platform_mapping()

            if not platform_mapping or not platform_mapping.get('platforms'):
                logger.warning("未从API获取到平台数据，尝试使用备用文档")
                return self._load_fallback_docs()

            # 将API数据转换为文档格式供LLM使用
            doc_content = self._format_platform_data_for_llm(platform_mapping)

            logger.info(f"已从API加载平台数据，共 {platform_mapping.get('total_count', 0)} 个平台")
            return doc_content

        except Exception as e:
            logger.warning(f"从API加载平台数据失败: {str(e)}，尝试使用备用文档")
            return self._load_fallback_docs()

    def _load_fallback_docs(self) -> str:
        """加载备用平台域名文档"""
        try:
            docs_path = os.path.join(os.path.dirname(__file__), 'platform-domains.md')
            if os.path.exists(docs_path):
                with open(docs_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    logger.info(f"已加载备用平台域名文档，包含 {len(content.splitlines())} 行")
                    return content
            else:
                logger.warning(f"备用平台域名文档不存在: {docs_path}")
                return ""
        except Exception as e:
            logger.warning(f"加载备用平台域名文档失败: {str(e)}")
            return ""

    def _format_platform_data_for_llm(self, platform_mapping: dict) -> str:
        """将API数据格式化为LLM可理解的文档格式"""
        try:
            platforms = platform_mapping.get('platforms', [])
            total_count = platform_mapping.get('total_count', 0)
            dns_providers = platform_mapping.get('dns_providers', {})

            # 构建文档内容
            supported_count = platform_mapping.get('supported_count', 0)
            unsupported_count = platform_mapping.get('unsupported_count', 0)

            doc_lines = [
                "# 企业平台域名映射表",
                "",
                f"## 概览",
                f"- 平台总数: {total_count}",
                f"- 支持自动签发: {supported_count} 个（仅Letsencrypt + txt验证）",
                f"- 不支持自动签发: {unsupported_count} 个",
                f"- DNS提供商分布: {dns_providers}",
                "",
                "## 支持自动签发的平台列表",
                "（仅包含签发机构为Letsencrypt且验证方式为txt的平台）",
                ""
            ]

            # 按DNS提供商分组
            grouped_platforms = {}
            for platform in platforms:
                dns_provider = platform.get('dns_provider', 'unknown')
                if dns_provider not in grouped_platforms:
                    grouped_platforms[dns_provider] = []
                grouped_platforms[dns_provider].append(platform)

            # 生成分组文档
            for dns_provider, provider_platforms in grouped_platforms.items():
                dns_name_map = {
                    'aws': 'AWS Route53',
                    'aliyun': '阿里云DNS',
                    'cloudflare': 'Cloudflare DNS',
                    'unknown': '未知DNS提供商'
                }

                doc_lines.extend([
                    f"### {dns_name_map.get(dns_provider, dns_provider)} ({len(provider_platforms)}个平台)",
                    ""
                ])

                for platform in provider_platforms:
                    visible_name = platform.get('visible_name', '')
                    cert_name = platform.get('cert_name', '')
                    domains = platform.get('domains', [])

                    # 格式化域名列表
                    domain_str = ', '.join(domains) if domains else cert_name

                    doc_lines.append(f"- **{visible_name}**: {domain_str}")

                doc_lines.append("")

            return '\n'.join(doc_lines)

        except Exception as e:
            logger.error(f"格式化平台数据失败: {str(e)}")
            return "# 平台数据格式化失败\n请检查API连接和数据格式。"

    @log_tool_execution()
    async def _create_cloudflare_config_file(self) -> Optional[str]:
        """创建或验证Cloudflare DNS配置文件"""
        try:
            config_dir = os.path.join(self.config['cert_base_path'], 'config')
            os.makedirs(config_dir, exist_ok=True)

            config_file = os.path.join(config_dir, 'cloudflare.ini')

            # 检查是否已存在有效的配置文件
            if os.path.exists(config_file):
                logger.info(f"发现现有Cloudflare配置文件: {config_file}")
                with open(config_file, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
                    logger.info(f"现有配置文件内容:\n{existing_content}")

                # 验证现有配置文件是否包含必要信息
                if ('dns_cloudflare_email' in existing_content and 'dns_cloudflare_api_key' in existing_content) or 'dns_cloudflare_api_token' in existing_content:
                    logger.info("✅ 现有配置文件有效，直接使用")
                    return config_file
                else:
                    logger.warning("现有配置文件格式不正确，将重新创建")

            # 创建新的配置文件
            # 优先使用API Token（推荐），其次使用API Key
            if self.config['cloudflare_api_token']:
                config_content = f"""dns_cloudflare_api_token = {self.config['cloudflare_api_token']}
"""
            elif self.config['cloudflare_email'] and self.config['cloudflare_api_key']:
                config_content = f"""dns_cloudflare_email = {self.config['cloudflare_email']}
dns_cloudflare_api_key = {self.config['cloudflare_api_key']}
"""
            else:
                logger.error("Cloudflare配置不完整：需要API Token或者Email+API Key")
                return None

            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)

            # 设置文件权限（仅所有者可读写）
            try:
                os.chmod(config_file, 0o600)
            except OSError:
                # Windows下可能不支持chmod，忽略错误
                pass

            logger.info(f"Cloudflare DNS配置文件创建成功: {config_file}")

            # 验证配置文件内容
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.info(f"配置文件内容:\n{content}")

            # 验证API Key格式
            if self.config['cloudflare_api_key']:
                api_key = self.config['cloudflare_api_key']
                logger.info(f"Cloudflare API Key配置完成，长度: {len(api_key)}字符")

                # 基本格式检查
                import re
                if not re.match(r'^[a-fA-F0-9]+$', api_key):
                    logger.info("API Key包含非十六进制字符，可能是新格式的API Key")

                logger.info("API Key格式验证完成")

            # 测试Cloudflare API连接
            await self._test_cloudflare_api()

            return config_file

        except Exception as e:
            logger.error(f"创建Cloudflare DNS配置文件失败: {str(e)}")
            return None

    @log_tool_execution()
    async def _test_cloudflare_api(self):
        """测试Cloudflare API连接"""
        try:
            import requests

            email = self.config['cloudflare_email']
            api_key = self.config['cloudflare_api_key']

            if not email or not api_key:
                logger.warning("Cloudflare API凭据不完整，跳过API测试")
                return

            # 测试API连接
            headers = {
                'X-Auth-Email': email,
                'X-Auth-Key': api_key,
                'Content-Type': 'application/json'
            }

            logger.info("测试Cloudflare API连接...")
            response = requests.get('https://api.cloudflare.com/client/v4/user', headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    logger.info("✅ Cloudflare API连接测试成功")
                    user_email = data.get('result', {}).get('email', 'Unknown')
                    logger.info(f"API用户邮箱: {user_email}")
                else:
                    logger.error("❌ Cloudflare API返回错误")
                    logger.error(f"错误信息: {data.get('errors', [])}")
            else:
                logger.error(f"❌ Cloudflare API连接失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")

        except Exception as e:
            logger.warning(f"Cloudflare API测试失败: {str(e)}")
            logger.warning("这可能不影响证书签发，继续执行...")

    @log_tool_execution()
    async def _create_aliyun_config_file(self) -> Optional[str]:
        """创建阿里云DNS配置文件"""
        try:
            config_dir = os.path.join(self.config['cert_base_path'], 'config')
            os.makedirs(config_dir, exist_ok=True)

            config_file = os.path.join(config_dir, 'aliyun.ini')

            config_content = f"""# Aliyun DNS API credentials
dns_aliyun_access_key = {self.config['alicloud_access_key']}
dns_aliyun_access_key_secret = {self.config['alicloud_secret_key']}
"""

            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)

            # 设置文件权限（仅所有者可读写）
            os.chmod(config_file, 0o600)

            logger.info(f"阿里云DNS配置文件创建成功: {config_file}")
            return config_file

        except Exception as e:
            logger.error(f"创建阿里云DNS配置文件失败: {str(e)}")
            return None

    @log_tool_execution()
    async def _issue_certificate_aliyun_dns(self, domains: List[str], email: str = None,
                                           key_type: str = "rsa", preferred_chain: str = "ISRG Root X1",
                                           cert_name: str = None, force_renewal: bool = False) -> Dict:
        """使用阿里云DNS自动化签发证书"""
        try:
            logger.info(f"开始为域名 {domains} 签发证书，使用阿里云DNS自动化验证")

            # 如果没有指定证书名称，使用第一个域名（去掉通配符）作为证书名称
            # 确保每个域名都有唯一的证书名称，避免不同域名的证书冲突
            if not cert_name and domains:
                # 使用完整的第一个域名作为证书名称，只去掉通配符前缀
                cert_name = domains[0].replace('*.', '')
                logger.info(f"自动设置证书名称: {cert_name}")

                # 检查是否存在同名证书，如果存在且域名不同，则添加后缀避免冲突
                existing_cert_path = os.path.join(self.config['cert_base_path'], 'live', cert_name)
                if os.path.exists(existing_cert_path):
                    # 检查现有证书的域名
                    try:
                        renewal_conf = os.path.join(self.config['cert_base_path'], 'renewal', f'{cert_name}.conf')
                        if os.path.exists(renewal_conf):
                            with open(renewal_conf, 'r') as f:
                                conf_content = f.read()
                                # 如果当前域名不在现有证书的域名列表中，说明是不同的域名
                                if domains[0] not in conf_content:
                                    # 为新域名生成唯一的证书名称
                                    import time
                                    timestamp = str(int(time.time()))[-6:]  # 使用时间戳后6位
                                    cert_name = f"{cert_name}-{timestamp}"
                                    logger.warning(f"检测到证书名称冲突，自动生成唯一证书名称: {cert_name}")
                    except Exception as e:
                        logger.debug(f"检查现有证书时出错: {e}")
                        pass

            # 创建阿里云DNS钩子脚本路径
            script_path = os.path.join(os.path.dirname(__file__), 'aliyun_dns_script.py')

            if not os.path.exists(script_path):
                return {
                    "success": False,
                    "message": "阿里云DNS脚本不存在",
                    "domains": domains
                }

            # 构建certbot命令
            cmd = ['certbot', 'certonly', '--non-interactive', '--agree-tos']

            # 添加邮箱
            if email:
                cmd.extend(['--email', email])
            else:
                cmd.extend(['--email', self.config['default_email']])

            # 添加域名（支持多个域名和通配符）
            for domain in domains:
                cmd.extend(['-d', domain])

            # 添加密钥类型
            if key_type:
                cmd.extend(['--key-type', key_type])

            # 添加首选证书链
            if preferred_chain:
                cmd.extend(['--preferred-chain', preferred_chain])

            # 添加证书名称（用于更新现有证书或切换密钥类型）
            if cert_name:
                cmd.extend(['--cert-name', cert_name])

            # 强制更新
            if force_renewal:
                cmd.append('--force-renewal')

            # 使用手动模式和DNS验证，配置钩子脚本
            python_path = sys.executable  # 获取当前Python解释器路径

            # Windows路径处理：将反斜杠转换为正斜杠，避免路径问题
            python_path_normalized = python_path.replace('\\', '/')
            script_path_normalized = script_path.replace('\\', '/')

            # 创建钩子脚本包装器，避免Windows环境变量问题
            auth_hook_script = os.path.join(os.path.dirname(script_path), 'auth_hook.py')
            cleanup_hook_script = os.path.join(os.path.dirname(script_path), 'cleanup_hook.py')

            # 创建认证钩子脚本
            auth_hook_content = f'''#!/usr/bin/env python
import os
import sys
import subprocess

# 获取certbot环境变量
domain = os.environ.get('CERTBOT_DOMAIN', '')
validation = os.environ.get('CERTBOT_VALIDATION', '')

# 调用阿里云DNS脚本
cmd = [
    r"{python_path}",
    r"{script_path}",
    "add",
    domain,
    "_acme-challenge",
    validation,
    "{self.config["alicloud_access_key"]}",
    "{self.config["alicloud_secret_key"]}"
]

result = subprocess.run(cmd, capture_output=True, text=True)
print(result.stdout)
if result.stderr:
    print(result.stderr, file=sys.stderr)
sys.exit(result.returncode)
'''

            # 创建清理钩子脚本
            cleanup_hook_content = f'''#!/usr/bin/env python
import os
import sys
import subprocess

# 获取certbot环境变量
domain = os.environ.get('CERTBOT_DOMAIN', '')
validation = os.environ.get('CERTBOT_VALIDATION', '')

# 调用阿里云DNS脚本
cmd = [
    r"{python_path}",
    r"{script_path}",
    "clean",
    domain,
    "_acme-challenge",
    validation,
    "{self.config["alicloud_access_key"]}",
    "{self.config["alicloud_secret_key"]}"
]

result = subprocess.run(cmd, capture_output=True, text=True)
print(result.stdout)
if result.stderr:
    print(result.stderr, file=sys.stderr)
sys.exit(result.returncode)
'''

            # 写入钩子脚本文件
            with open(auth_hook_script, 'w', encoding='utf-8') as f:
                f.write(auth_hook_content)
            with open(cleanup_hook_script, 'w', encoding='utf-8') as f:
                f.write(cleanup_hook_content)

            # 构建钩子命令
            auth_hook = f'{python_path_normalized} {auth_hook_script.replace(chr(92), "/")}'
            cleanup_hook = f'{python_path_normalized} {cleanup_hook_script.replace(chr(92), "/")}'

            cmd.extend([
                '--manual',
                '--preferred-challenges', 'dns',
                '--manual-auth-hook', auth_hook,
                '--manual-cleanup-hook', cleanup_hook
            ])

            # 配置证书存储路径
            cmd.extend(['--config-dir', self.config['cert_base_path']])
            cmd.extend(['--work-dir', os.path.join(self.config['cert_base_path'], 'work')])
            cmd.extend(['--logs-dir', os.path.join(self.config['cert_base_path'], 'logs')])

            # 测试环境
            if self.config['staging']:
                cmd.append('--staging')

            logger.info(f"执行命令: {' '.join(cmd[:10])}... (钩子脚本已配置)")

            # 执行certbot命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

            if result.returncode == 0:
                logger.info("阿里云DNS证书签发成功")

                # 复制证书到输出目录
                await self._copy_certificates_to_output(domains[0])

                return {
                    "success": True,
                    "message": "阿里云DNS证书签发成功",
                    "domains": domains,
                    "provider": "aliyun",
                    "output": result.stdout
                }
            else:
                logger.error(f"阿里云DNS证书签发失败: {result.stderr}")
                return {
                    "success": False,
                    "message": "阿里云DNS证书签发失败",
                    "domains": domains,
                    "error": result.stderr,
                    "output": result.stdout
                }

        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "message": "阿里云DNS证书签发超时",
                "domains": domains
            }
        except Exception as e:
            logger.error(f"阿里云DNS证书签发失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": f"阿里云DNS证书签发失败: {str(e)}",
                "domains": domains
            }

    @log_tool_execution()
    async def _issue_certificate_cloudflare_dns(self, domains: List[str], email: str = None,
                                               key_type: str = "rsa", preferred_chain: str = "ISRG Root X1",
                                               cert_name: str = None, force_renewal: bool = False) -> Dict:
        """使用Cloudflare DNS签发证书"""
        try:
            logger.info(f"开始为域名 {domains} 签发证书，使用Cloudflare DNS验证")

            # 创建Cloudflare配置文件
            config_file = await self._create_cloudflare_config_file()
            if not config_file:
                return {
                    "success": False,
                    "message": "Cloudflare DNS配置文件创建失败",
                    "domains": domains
                }

            # 如果没有指定证书名称，使用第一个域名（去掉通配符）作为证书名称
            if not cert_name and domains:
                cert_name = domains[0].replace('*.', '')
                logger.info(f"自动设置证书名称: {cert_name}")

            # 构建certbot命令
            cmd = ['certbot', 'certonly', '--non-interactive', '--agree-tos']

            # 添加邮箱
            if email:
                cmd.extend(['--email', email])
            else:
                cmd.extend(['--email', self.config['default_email']])

            # 添加域名（支持多个域名和通配符）
            for domain in domains:
                cmd.extend(['-d', domain])

            # 添加密钥类型
            if key_type:
                cmd.extend(['--key-type', key_type])

            # 添加首选证书链
            if preferred_chain:
                cmd.extend(['--preferred-chain', preferred_chain])

            # 添加证书名称
            if cert_name:
                cmd.extend(['--cert-name', cert_name])

            # 强制更新
            if force_renewal:
                cmd.append('--force-renewal')

            # 使用Cloudflare DNS插件
            # 确保使用正确的路径格式
            config_file_normalized = config_file.replace('\\', '/')
            cmd.extend([
                '--dns-cloudflare',
                '--dns-cloudflare-credentials', config_file_normalized
            ])

            # 配置证书存储路径（使用绝对路径避免权限问题）
            config_dir = os.path.abspath(self.config['cert_base_path'])
            work_dir = os.path.abspath(os.path.join(self.config['cert_base_path'], 'work'))
            logs_dir = os.path.abspath(os.path.join(self.config['cert_base_path'], 'logs'))

            # 确保目录存在
            os.makedirs(config_dir, exist_ok=True)
            os.makedirs(work_dir, exist_ok=True)
            os.makedirs(logs_dir, exist_ok=True)

            cmd.extend(['--config-dir', config_dir])
            cmd.extend(['--work-dir', work_dir])
            cmd.extend(['--logs-dir', logs_dir])

            # 测试环境
            if self.config['staging']:
                cmd.append('--staging')

            logger.info(f"执行命令: {' '.join(cmd[:10])}... (Cloudflare DNS插件)")

            recursion_limit=25,
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

            if result.returncode == 0:
                logger.info("Cloudflare DNS证书签发成功")

                # 复制证书到输出目录
                await self._copy_certificates_to_output(domains[0])

                return {
                    "success": True,
                    "message": "Cloudflare DNS证书签发成功",
                    "domains": domains,
                    "provider": "cloudflare",
                    "output": result.stdout
                }
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                logger.error(f"Cloudflare DNS证书签发失败: {error_msg}")

                # 分析常见错误并提供解决建议
                if "administrative rights" in error_msg.lower():
                    suggestion = "请以管理员权限运行PowerShell后重试"
                elif "unknown x-auth-key" in error_msg.lower():
                    suggestion = "请检查Cloudflare API Key和邮箱配置是否正确"
                elif "zone_id" in error_msg.lower():
                    suggestion = "请确认域名已添加到您的Cloudflare账户中"
                elif "rate limit" in error_msg.lower():
                    suggestion = "触发了Let's Encrypt速率限制，请稍后重试"
                else:
                    suggestion = "请检查网络连接和域名配置"

                return {
                    "success": False,
                    "message": "Cloudflare DNS证书签发失败",
                    "domains": domains,
                    "error": error_msg,
                    "suggestion": suggestion,
                    "output": result.stdout
                }

        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "message": "Cloudflare DNS证书签发超时",
                "domains": domains
            }
        except Exception as e:
            logger.error(f"Cloudflare DNS证书签发失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": f"Cloudflare DNS证书签发失败: {str(e)}",
                "domains": domains
            }

    @log_tool_execution()
    async def _issue_certificate_certbot(self, domains: List[str], provider: str, email: str = None,
                                        key_type: str = "rsa", preferred_chain: str = "ISRG Root X1",
                                        cert_name: str = None, force_renewal: bool = False) -> Dict:
        """使用certbot命令行工具签发证书"""
        try:
            logger.info(f"开始为域名 {domains} 签发证书，使用 {provider} DNS验证")

            # 如果没有指定证书名称，使用第一个域名（去掉通配符）作为证书名称
            # 确保每个域名都有唯一的证书名称，避免不同域名的证书冲突
            if not cert_name and domains:
                # 使用完整的第一个域名作为证书名称，只去掉通配符前缀
                cert_name = domains[0].replace('*.', '')
                logger.info(f"自动设置证书名称: {cert_name}")

                # 检查是否存在同名证书，如果存在且域名不同，则添加后缀避免冲突
                existing_cert_path = os.path.join(self.config['cert_base_path'], 'live', cert_name)
                if os.path.exists(existing_cert_path):
                    # 检查现有证书的域名
                    try:
                        renewal_conf = os.path.join(self.config['cert_base_path'], 'renewal', f'{cert_name}.conf')
                        if os.path.exists(renewal_conf):
                            with open(renewal_conf, 'r') as f:
                                conf_content = f.read()
                                # 如果当前域名不在现有证书的域名列表中，说明是不同的域名
                                if domains[0] not in conf_content:
                                    # 为新域名生成唯一的证书名称
                                    import time
                                    timestamp = str(int(time.time()))[-6:]  # 使用时间戳后6位
                                    cert_name = f"{cert_name}-{timestamp}"
                                    logger.warning(f"检测到证书名称冲突，自动生成唯一证书名称: {cert_name}")
                    except Exception as e:
                        logger.debug(f"检查现有证书时出错: {e}")
                        pass

            # 构建certbot命令
            cmd = ['certbot', 'certonly', '--non-interactive', '--agree-tos']
            
            # 添加邮箱
            if email:
                cmd.extend(['--email', email])
            else:
                cmd.extend(['--email', self.config['default_email']])
            
            # 添加域名（支持多个域名和通配符）
            for domain in domains:
                cmd.extend(['-d', domain])

            # 添加密钥类型
            if key_type:
                cmd.extend(['--key-type', key_type])

            # 添加首选证书链
            if preferred_chain:
                cmd.extend(['--preferred-chain', preferred_chain])

            # 添加证书名称（用于更新现有证书或切换密钥类型）
            if cert_name:
                cmd.extend(['--cert-name', cert_name])

            # 强制更新
            if force_renewal:
                cmd.append('--force-renewal')

            # 配置DNS提供商
            env = os.environ.copy()

            if provider == 'aws':
                cmd.append('--dns-route53')
                env.update({
                    'AWS_ACCESS_KEY_ID': self.config['aws_access_key_id'],
                    'AWS_SECRET_ACCESS_KEY': self.config['aws_secret_access_key']
                })
            elif provider == 'aliyun' or provider == 'alicloud':
                # 使用阿里云DNS自动化
                return await self._issue_certificate_aliyun_dns(domains, email, key_type, preferred_chain, cert_name, force_renewal)
            elif provider == 'cloudflare':
                # 使用Cloudflare DNS
                return await self._issue_certificate_cloudflare_dns(domains, email, key_type, preferred_chain, cert_name, force_renewal)
            else:
                return {
                    "success": False,
                    "message": f"不支持的DNS提供商: {provider}，支持的提供商: aws, aliyun, cloudflare",
                    "domains": domains
                }
            
            # 配置证书存储路径
            cmd.extend(['--config-dir', self.config['cert_base_path']])
            cmd.extend(['--work-dir', os.path.join(self.config['cert_base_path'], 'work')])
            cmd.extend(['--logs-dir', os.path.join(self.config['cert_base_path'], 'logs')])
            
            # 测试环境
            if self.config['staging']:
                cmd.append('--staging')
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            # 执行certbot命令
            result = subprocess.run(cmd, capture_output=True, text=True, env=env, timeout=300)
            
            if result.returncode == 0:
                logger.info("证书签发成功")
                
                # 复制证书到输出目录
                await self._copy_certificates_to_output(domains[0])
                
                return {
                    "success": True,
                    "message": "证书签发成功",
                    "domains": domains,
                    "provider": provider,
                    "output": result.stdout
                }
            else:
                logger.error(f"证书签发失败: {result.stderr}")
                return {
                    "success": False,
                    "message": "证书签发失败",
                    "domains": domains,
                    "error": result.stderr,
                    "output": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "message": "证书签发超时",
                "domains": domains
            }
        except Exception as e:
            logger.error(f"签发证书时发生错误: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": f"签发证书时发生错误: {str(e)}",
                "domains": domains
            }

    @log_tool_execution()
    async def _copy_certificates_to_output(self, domain: str) -> bool:
        """复制证书到输出目录"""
        try:
            import shutil
            
            output_path = os.path.join(self.config['cert_output_path'], domain)
            os.makedirs(output_path, exist_ok=True)
            
            cert_files = ['cert.pem', 'privkey.pem', 'chain.pem', 'fullchain.pem']
            source_path = os.path.join(self.config['cert_base_path'], 'live', domain)
            
            for cert_file in cert_files:
                source_file = os.path.join(source_path, cert_file)
                dest_file = os.path.join(output_path, cert_file)
                
                if os.path.exists(source_file):
                    shutil.copy2(source_file, dest_file)
                    logger.debug(f"复制证书文件: {cert_file}")
            
            logger.info(f"证书文件已复制到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"复制证书文件失败: {str(e)}")
            return False

    @log_tool_execution()
    async def _check_online_certificate(self, domain: str) -> Dict:
        """检查在线证书状态"""
        try:
            context = ssl.create_default_context()
            
            with socket.create_connection((domain, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert_info = ssock.getpeercert()
                    
                    return {
                        "domain": domain,
                        "subject": str(cert_info.get('subject', '')),
                        "issuer": str(cert_info.get('issuer', '')),
                        "valid_from": cert_info.get('notBefore', ''),
                        "valid_to": cert_info.get('notAfter', ''),
                        "serial_number": cert_info.get('serialNumber', ''),
                        "online_status": "valid"
                    }
                
        except Exception as e:
            logger.error(f"检查在线证书失败: {str(e)}")
            return {"error": str(e), "online_status": "failed"}

    @log_tool_execution()
    async def issue_certificate_tool(self):
        """签发证书工具"""
        @tool(args_schema=IssueCertificateSchema)
        async def issue_certificate(domains: List[str], provider: str, email: str = None,
                                   force_renewal: bool = False, key_type: str = "rsa",
                                   preferred_chain: str = "ISRG Root X1", cert_name: str = None) -> Dict:
            """
            为指定域名签发SSL证书

            Args:
                domains: 域名列表，支持多个域名和通配符域名
                provider: DNS提供商 (aws=AWS Route53, aliyun=阿里云DNS, cloudflare=Cloudflare DNS)
                email: Let's Encrypt 账户邮箱
                force_renewal: 是否强制更新已存在的证书
                key_type: 密钥类型 (rsa, ecdsa)
                preferred_chain: 首选证书链
                cert_name: 证书名称，用于更新现有证书或切换密钥类型

            Returns:
                签发结果
            """
            return await self._issue_certificate_certbot(domains, provider, email, key_type, preferred_chain, cert_name, force_renewal)
        
        return issue_certificate

    @log_tool_execution()
    async def check_certificate_status_tool(self):
        """检查证书状态工具"""
        @tool(args_schema=CheckCertificateStatusSchema)
        async def check_certificate_status(domain: str, check_online: bool = True) -> Dict:
            """
            检查证书状态
            
            Args:
                domain: 域名
                check_online: 是否检查在线证书状态
                
            Returns:
                证书状态信息
            """
            try:
                result = {
                    "success": True,
                    "message": "证书状态检查完成",
                    "domain": domain
                }
                
                if check_online:
                    online_cert_info = await self._check_online_certificate(domain)
                    result["online_certificate"] = online_cert_info
                
                return result
                    
            except Exception as e:
                logger.error(f"检查证书状态时发生错误: {str(e)}", exc_info=True)
                return {
                    "success": False,
                    "message": f"检查证书状态时发生错误: {str(e)}",
                    "domain": domain
                }
        
        return check_certificate_status

    @log_tool_execution()
    async def search_platforms_tool(self):
        """创建平台搜索工具"""
        @tool
        def search_platforms(query: str) -> str:
            """
            搜索匹配的企业平台

            Args:
                query: 搜索关键词，支持平台名称、域名、服务名称等

            Returns:
                匹配的平台列表，包含平台名称、域名和DNS提供商信息
            """
            try:
                from .certificate_api_client import CertificateAPIClient

                api_client = CertificateAPIClient()
                search_results = api_client.search_platforms(query, include_unsupported=True)

                supported_platforms = search_results.get('supported', [])
                unsupported_platforms = search_results.get('unsupported', [])

                if not supported_platforms and not unsupported_platforms:
                    return f"未找到匹配 '{query}' 的平台。请尝试其他关键词。"

                result_lines = []

                # 显示支持的平台
                if supported_platforms:
                    result_lines.append(f"✅ 找到 {len(supported_platforms)} 个支持自动签发的平台：\n")

                    for i, platform in enumerate(supported_platforms, 1):
                        visible_name = platform.get('visible_name', '')
                        cert_name = platform.get('cert_name', '')
                        domains = platform.get('domains', [])
                        dns_server = platform.get('dns_server', '')

                        domain_str = ', '.join(domains[:3])  # 只显示前3个域名
                        if len(domains) > 3:
                            domain_str += f" (共{len(domains)}个域名)"

                        result_lines.append(
                            f"{i}. **{visible_name}** ✅\n"
                            f"   - 证书名称: {cert_name}\n"
                            f"   - 域名: {domain_str}\n"
                            f"   - DNS提供商: {dns_server}\n"
                        )

                # 显示不支持的平台
                if unsupported_platforms:
                    result_lines.append(f"\n❌ 找到 {len(unsupported_platforms)} 个不支持自动签发的平台：\n")

                    for i, platform in enumerate(unsupported_platforms, 1):
                        visible_name = platform.get('visible_name', '')
                        cert_name = platform.get('cert_name', '')
                        dns_server = platform.get('dns_server', '')
                        reasons = platform.get('unsupported_reasons', [])

                        result_lines.append(
                            f"{i}. **{visible_name}** ❌\n"
                            f"   - 证书名称: {cert_name}\n"
                            f"   - DNS提供商: {dns_server}\n"
                            f"   - 不支持原因: {'; '.join(reasons)}\n"
                        )

                return '\n'.join(result_lines)

            except Exception as e:
                logger.error(f"搜索平台失败: {str(e)}")
                return f"搜索平台时发生错误: {str(e)}"

        return search_platforms

    @log_tool_execution()
    async def get_tools(self) -> List:
        """获取所有可用工具的列表"""
        tools = [
            await self.issue_certificate_tool(),
            await self.check_certificate_status_tool(),
            await self.search_platforms_tool(),
        ]
        return tools

    @log_tool_execution()
    async def invoke(self, task: str) -> Any:
        """使用 LangChain 的 agent_executor 模式调用工具"""
        class ResultModel(BaseModel):
            task: str = Field(..., description="任务执行内容")
            status: str = Field(..., description="操作结果状态，success | failed")
            result: str = Field(..., description="执行完任务后返回的结果，不更改工具返回的结果描述")
            failed_reason: Optional[str] = Field(None, description="执行失败时的原因，详细描述，如执行了什么操作，返回了什么结果")

        try:
            tools = await self.get_tools()
            
            prompt_file_path = os.path.join(os.path.dirname(__file__), 'letsencrypt_prompt.md')
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                system_prompt = f.read().strip()

            # 如果有平台文档，添加到系统提示中
            if self.platform_docs:
                system_prompt += f"\n\n## 平台域名映射表\n\n{self.platform_docs}"
            
            agent_executor = create_react_agent(
                llm.bind_tools(tools, parallel_tool_calls=False),
                tools=tools,
                response_format=ResultModel
            )
            
            logger.info(f"执行任务: {task}")
            response = await agent_executor.ainvoke(
                input={
                    "messages": [
                        SystemMessage(system_prompt),
                        HumanMessage(task),
                    ]
                },
                config=RunnableConfig(
                    recursion_limit=25,
                ),
            )
            
            structured_response = response.get("structured_response")
            logger.info(f"任务执行完成，状态: {structured_response.status}")
            return structured_response
            
        except Exception as e:
            logger.error(f"执行任务时发生错误: {str(e)}", exc_info=True)
            return {
                "task": task,
                "status": "failed",
                "result": "任务执行失败",
                "failed_reason": f"执行任务时发生错误: {str(e)}"
            }


# 为了兼容性，创建别名
LetsEncryptAgent = FixedLetsEncryptAgent
