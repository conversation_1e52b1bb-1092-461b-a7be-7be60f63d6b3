#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix混合客户端

优先使用MCP服务，失败时回退到直接API调用，确保可靠性。
"""

import os
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
from utils.logging_config import logger, log_tool_execution

# 尝试导入MCP客户端
try:
    from .zabbix_mcp_simple import ZabbixMCPSimple
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False
    logger.warning("MCP客户端不可用，将使用直接API")

# 尝试导入直接API客户端
try:
    from .zabbix_simple_client import ZabbixSimpleClient
    DIRECT_API_AVAILABLE = True
except ImportError:
    DIRECT_API_AVAILABLE = False
    logger.warning("直接API客户端不可用")


class ZabbixHybridClient:
    """
    Zabbix混合客户端

    优先使用MCP服务，失败时回退到直接API调用。
    """

    def __init__(self, auto_start_server: bool = True):
        """
        初始化Zabbix混合客户端

        Args:
            auto_start_server: 是否自动启动MCP服务器
        """
        self.auto_start_server = auto_start_server
        self.mcp_client = None
        self.direct_client = None
        self.use_mcp = False

        logger.info("初始化Zabbix混合客户端")
        self._initialize_clients()

    def _initialize_clients(self):
        """初始化客户端"""
        # 尝试初始化MCP客户端
        if MCP_AVAILABLE:
            try:
                self.mcp_client = ZabbixMCPSimple(self.auto_start_server)
                logger.info("MCP客户端初始化成功")
            except Exception as e:
                logger.warning(f"MCP客户端初始化失败: {e}")

        # 初始化直接API客户端作为备用
        if DIRECT_API_AVAILABLE:
            try:
                self.direct_client = ZabbixSimpleClient()
                logger.info("直接API客户端初始化成功")
            except Exception as e:
                logger.warning(f"直接API客户端初始化失败: {e}")

    async def _try_mcp_connection(self) -> bool:
        """尝试MCP连接"""
        if not self.mcp_client:
            return False

        try:
            # 尝试连接，设置较短的超时
            connected = await asyncio.wait_for(
                self.mcp_client.connect(),
                timeout=15.0  # 15秒超时
            )
            if connected:
                logger.info("MCP连接成功")
                self.use_mcp = True
                return True
        except asyncio.TimeoutError:
            logger.warning("MCP连接超时，切换到直接API")
        except Exception as e:
            logger.warning(f"MCP连接失败: {e}，切换到直接API")

        return False

    def _ensure_direct_client(self) -> bool:
        """确保直接API客户端可用"""
        if not self.direct_client:
            return False

        try:
            return self.direct_client.test_connection()
        except Exception as e:
            logger.error(f"直接API客户端连接失败: {e}")
            return False

    async def _call_api_method(self, method_name: str, *args, **kwargs):
        """调用API方法，优先使用MCP，失败时使用直接API"""

        # 首先尝试MCP（如果还没有尝试过连接）
        if self.mcp_client and not self.use_mcp:
            if await self._try_mcp_connection():
                self.use_mcp = True

        # 如果MCP可用，尝试使用MCP
        if self.use_mcp and self.mcp_client:
            try:
                method = getattr(self.mcp_client, method_name)
                if asyncio.iscoroutinefunction(method):
                    return await method(*args, **kwargs)
                else:
                    return method(*args, **kwargs)
            except Exception as e:
                logger.warning(f"MCP调用失败: {e}，切换到直接API")
                self.use_mcp = False

        # 回退到直接API
        if self.direct_client:
            if self._ensure_direct_client():
                try:
                    method = getattr(self.direct_client, method_name)
                    if asyncio.iscoroutinefunction(method):
                        return await method(*args, **kwargs)
                    else:
                        return method(*args, **kwargs)
                except Exception as e:
                    logger.error(f"直接API调用也失败: {e}")
                    raise

        raise Exception("所有客户端都不可用")

    # 以下是ZabbixAgent需要的方法

    async def get_hostgroup_id(self, hostgroup_name: str) -> Optional[str]:
        """根据主机组名称获取主机组ID"""
        return await self._call_api_method("get_hostgroup_id", hostgroup_name)

    async def get_host_ids_in_group(self, groupid: str) -> List[str]:
        """根据主机组ID获取主机ID列表"""
        return await self._call_api_method("get_host_ids_in_group", groupid)

    async def get_item_ids(self, hostids: List[str], keys: List[str]) -> Dict[str, List[str]]:
        """根据主机ID和监控项key获取监控项ID字典"""
        return await self._call_api_method("get_item_ids", hostids, keys)

    async def get_history(self, itemids: List[str], time_from: int, time_till: int = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取历史数据"""
        return await self._call_api_method("get_history", itemids, time_from, time_till, limit)

    async def get_trends(self, itemids: List[str], time_from: int, time_till: int = None) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        return await self._call_api_method("get_trends", itemids, time_from, time_till)

    async def get_problems(self, groupids: List[str] = None, severities: List[int] = None,
                          time_from: int = None, time_till: int = None, recent: bool = True,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """获取问题数据"""
        return await self._call_api_method("get_problems", groupids, severities, time_from, time_till, recent, limit)

    async def get_triggers(self, groupids: List[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取触发器数据"""
        return await self._call_api_method("get_triggers", groupids, limit)

    async def disconnect(self):
        """断开连接"""
        if self.mcp_client:
            try:
                await self.mcp_client.disconnect()
            except Exception as e:
                logger.warning(f"MCP客户端断开连接失败: {e}")

        # 直接API客户端通常不需要显式断开连接