# -*- coding: utf-8 -*-

"""
File: tencent_email_agent.py
Author: HuangJun
Date: 2025/6/17

腾讯企业邮箱管理代理，提供企业邮箱账号的创建、删除、查询和更新等功能。
"""

import os
import random
import string
import logging
from typing import Dict, Optional, List, Any, Union, Tuple

import requests
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from langchain_core.tools import tool
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent

from llm.llm import llm
from utils.logging_config import log_tool_execution
from agents.tencent.tencent_schema import (
    CreateEmailAccountSchema,
    DeleteEmailAccountSchema,
    GetEmailAccountSchema,
    UpdateEmailAccountSchema
)

# 配置日志
logger = logging.getLogger(__name__)

class TencentEmailAgent:
    """腾讯企业邮箱管理代理
    
    提供企业邮箱账号的创建、删除、查询和更新等功能，通过腾讯企业邮箱API实现。
    使用LangChain和Claude 3.7模型处理自然语言指令，转换为API调用。
    """
    
    def __init__(self):
        """初始化腾讯企业邮箱管理代理"""
        load_dotenv()
        
        # 加载配置
        self.config = self._load_config()
        self.access_token = None
        
        # 验证必要的环境变量
        self._validate_config()

    @log_tool_execution()
    def _validate_config(self) -> None:
        """验证配置是否完整"""
        required_vars = ['TENCENT_SECRET_ID', 'TENCENT_SECRET_KEY', 
                         'TENCENT_EMAIL_DOMAIN', 'TENCENT_EMAIL_API_ENDPOINT']
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            logger.error(f"缺少必要的环境变量: {', '.join(missing_vars)}")
            raise ValueError(f"缺少必要的环境变量: {', '.join(missing_vars)}")

    @log_tool_execution()
    def _load_config(self) -> Dict[str, str]:
        """加载腾讯企业邮箱配置"""
        return {
            'secret_id': os.getenv('TENCENT_SECRET_ID', ''),
            'secret_key': os.getenv('TENCENT_SECRET_KEY', ''),
            'domain': os.getenv('TENCENT_EMAIL_DOMAIN', ''),
            'api_endpoint': os.getenv('TENCENT_EMAIL_API_ENDPOINT', '')
        }

    @log_tool_execution()
    def _make_api_request(self, action: str, data: Optional[Dict] = None) -> Dict:
        """发起腾讯企业邮箱 API 请求
        
        Args:
            action: API操作名称
            data: 请求数据
            
        Returns:
            API响应数据
            
        Raises:
            Exception: API请求失败
        """
        # TODO: 实现腾讯企业邮箱 API 的签名和请求逻辑
        # 这里需要根据腾讯企业邮箱的具体 API 文档来实现
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.access_token}'
        }

        url = f"{self.config['api_endpoint']}/{action}"
        
        try:
            logger.debug(f"发起API请求: {url}, 数据: {data}")
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {str(e)}", exc_info=True)
            raise Exception(f"API 请求失败: {str(e)}")

    @log_tool_execution(tool_name="create_email_account_tool")
    async def create_email_account_tool(self):
        """创建企业邮箱账号工具"""
        @tool(args_schema=CreateEmailAccountSchema)
        async def create_email_account(
            display_name: str, 
            email: str, 
            password: Optional[str] = None,
            department: Optional[str] = None
        ) -> Dict:
            """创建企业邮箱账号
            
            Args:
                display_name: 用户显示名称
                email: 用户邮箱地址
                password: 用户初始密码，如果不提供将生成随机密码
                department: 用户所属部门
                
            Returns:
                创建结果，包含账号信息和密码
                
            Raises:
                Exception: 邮箱已存在或创建失败
            """
            # 检查邮箱是否已存在
            try:
                check_result = await self.get_email_account_tool()(email=email)
                if check_result.get("account"):
                    logger.warning(f"邮箱账号已存在: {email}")
                    raise Exception(f"邮箱账号 {email} 已存在")

                # 如果没有提供密码，生成一个随机密码
                if not password:
                    chars = string.ascii_letters + string.digits + "!@#$%^&*()"
                    password = ''.join(random.choice(chars) for _ in range(16))

                # 构建创建账号的数据
                account_data = {
                    "name": display_name,
                    "email": email,
                    "password": password,
                    "department": department
                }

                # 创建账号
                logger.info(f"创建邮箱账号: {email}")
                result = self._make_api_request("CreateAccount", account_data)

                return {
                    "result": {
                        "account": result,
                        "password": password
                    }
                }
            except Exception as e:
                logger.error(f"创建邮箱账号失败: {str(e)}", exc_info=True)
                raise
        return create_email_account

    @log_tool_execution(tool_name="delete_email_account_tool")
    async def delete_email_account_tool(self):
        """删除企业邮箱账号工具"""
        @tool(args_schema=DeleteEmailAccountSchema)
        async def delete_email_account(email: str) -> Dict:
            """删除企业邮箱账号
            
            Args:
                email: 用户邮箱地址
                
            Returns:
                删除结果
                
            Raises:
                Exception: 邮箱不存在或删除失败
            """
            try:
                # 检查邮箱是否存在
                check_result = await self.get_email_account_tool()(email=email)
                if not check_result.get("account"):
                    logger.warning(f"找不到邮箱账号: {email}")
                    raise Exception(f"找不到邮箱账号: {email}")

                # 删除账号
                logger.info(f"删除邮箱账号: {email}")
                self._make_api_request("DeleteAccount", {"email": email})
                return {"success": True}
            except Exception as e:
                logger.error(f"删除邮箱账号失败: {str(e)}", exc_info=True)
                raise
        return delete_email_account

    @log_tool_execution(tool_name="get_email_account_tool")
    async def get_email_account_tool(self):
        """获取企业邮箱账号信息工具"""
        @tool(args_schema=GetEmailAccountSchema)
        async def get_email_account(email: str) -> Dict:
            """获取企业邮箱账号信息
            
            Args:
                email: 用户邮箱地址
                
            Returns:
                账号信息
                
            Raises:
                Exception: 获取账号信息失败
            """
            try:
                logger.info(f"获取邮箱账号信息: {email}")
                result = self._make_api_request("GetAccount", {"email": email})
                return {"account": result}
            except Exception as e:
                logger.error(f"获取邮箱账号信息失败: {str(e)}", exc_info=True)
                raise
        return get_email_account

    @log_tool_execution(tool_name="update_email_account_tool")
    async def update_email_account_tool(self):
        """更新企业邮箱账号信息工具"""
        @tool(args_schema=UpdateEmailAccountSchema)
        async def update_email_account(email: str, update_data: Dict) -> Dict:
            """更新企业邮箱账号信息
            
            Args:
                email: 用户邮箱地址
                update_data: 要更新的用户数据
                
            Returns:
                更新后的账号信息
                
            Raises:
                Exception: 邮箱不存在或更新失败
            """
            try:
                # 检查邮箱是否存在
                check_result = await self.get_email_account_tool()(email=email)
                if not check_result.get("account"):
                    logger.warning(f"找不到邮箱账号: {email}")
                    raise Exception(f"找不到邮箱账号: {email}")

                # 更新账号
                logger.info(f"更新邮箱账号: {email}, 更新数据: {update_data}")
                result = self._make_api_request("UpdateAccount", {
                    "email": email,
                    **update_data
                })
                return {"account": result}
            except Exception as e:
                logger.error(f"更新邮箱账号失败: {str(e)}", exc_info=True)
                raise
        return update_email_account

    @log_tool_execution()
    async def get_tools(self) -> List:
        """获取所有可用工具的列表
        
        Returns:
            工具列表
        """
        tools = [
            await self.create_email_account_tool(),
            await self.delete_email_account_tool(),
            await self.get_email_account_tool(),
            await self.update_email_account_tool()
        ]
        return tools

    @log_tool_execution()
    async def invoke(self, task: str) -> Any:
        """使用 LangChain 的 agent_executor 模式调用工具
        
        Args:
            task: 用户任务描述
        
        Returns:
            agent_executor 的执行结果
            
        Raises:
            Exception: 调用失败
        """
        class ResultModel(BaseModel):
            task: str = Field(..., description="任务执行内容")
            status: str = Field(..., description="操作结果状态，success | failed")
            result: str = Field(..., description="执行完任务后返回的结果，不更改工具返回的结果描述")
            failed_reason: Optional[str] = Field(None, description="执行失败时的原因，详细描述，如执行了什么操作，返回了什么结果")
        
        try:
            # 获取所有工具
            tools = await self.get_tools()
            
            # 设置系统提示词
            # 从tencent_prompt.md文件中读取系统提示词
            prompt_file_path = os.path.join(os.path.dirname(__file__), 'tencent_prompt.md')
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                system_prompt = f.read().strip()
            
            # 创建 agent_executor
            agent_executor = create_react_agent(
                llm.bind_tools(tools, parallel_tool_calls=False),
                tools=tools,
                response_format=ResultModel
            )
            
            # 执行 agent
            logger.info(f"执行任务: {task}")
            response = await agent_executor.ainvoke(
                input={
                    "messages": [
                        SystemMessage(system_prompt),
                        HumanMessage(task),
                    ]
                },
                config=RunnableConfig(
                    recursion_limit=25,
                ),
            )
            
            structured_response = response.get("structured_response")
            logger.info(f"任务执行结果: {structured_response.status}")
            return structured_response
        except Exception as e:
            logger.error(f"执行任务失败: {str(e)}", exc_info=True)
            raise