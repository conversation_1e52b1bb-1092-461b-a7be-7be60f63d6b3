# LetsEncrypt SSL证书管理代理

## 概述

LetsEncrypt SSL证书管理代理是一个基于AI的自动化SSL证书管理工具，支持通过自然语言指令进行证书的签发和管理等操作。

**🔧 基于certbot实现**：使用Let's Encrypt官方certbot工具，确保稳定性和兼容性。

## 功能特性

### 🔐 证书签发
- **单域名证书** - 为单个域名签发SSL证书
- **密钥类型支持** - RSA和ECDSA密钥类型
- **证书链选择** - 支持指定首选证书链

### 🤖 智能平台匹配
- **实时API数据** - 从CMDB系统实时获取最新的平台域名映射数据
- **智能过滤** - 自动过滤支持自动签发的平台（仅Letsencrypt + txt验证）
- **动态平台支持** - 当前58个平台，其中42个支持自动签发，16个不支持
- **模糊名称识别** - 支持"研发支撑"、"gitlab"、"nezha海外"等模糊描述
- **智能搜索功能** - 提供search_platforms工具，区分支持和不支持的平台
- **原因说明** - 对不支持的平台提供详细的原因说明
- **自然语言理解** - 基于AI的智能平台和域名匹配
- **环境自动识别** - 自动识别"国内"、"海外"、"开发"、"测试"等环境

### 🔄 证书管理
- **状态查询** - 查看在线证书状态
- **证书存储** - 标准certbot证书存储结构（./letsencrypt）
- **自动复制** - 证书文件自动复制到数据目录（./data/certificates）
- **统一数据管理** - 所有证书输出文件集中在data目录

### 🌐 DNS提供商支持

| DNS提供商 | 支持域名 | 实现方式 | 状态 |
|-----------|----------|----------|------|
| **AWS Route53** | `*.inhand.design` | certbot-dns-route53插件 | ✅ 完全支持 |
| **阿里云DNS** | `*.inhand.online` | 自定义钩子脚本 | ✅ 完全支持 |
| **Cloudflare DNS** | `*.inhand.com` | certbot-dns-cloudflare插件 | ✅ 完全支持 |

#### 域名托管说明
- **AWS Route53**：托管 `inhand.design` 及其子域名
- **阿里云DNS**：托管 `inhand.online` 及其子域名
- **Cloudflare DNS**：托管 `inhand.com` 及其子域名
- **重要**：请确保使用正确的域名和对应的DNS提供商

## 环境要求

### 系统依赖
基于certbot实现，需要安装以下工具：

```bash
# 安装certbot和插件
pip install certbot certbot-dns-route53 certbot-dns-cloudflare

# 安装Python依赖
uv sync
```

### 系统要求
- Python 3.11+
- certbot命令行工具
- 网络连接（访问Let's Encrypt ACME服务器）
- DNS提供商API访问权限

### 环境变量配置

在 `.env` 文件中配置以下环境变量：

```bash
# LetsEncrypt SSL证书管理配置（纯Python版本）
CERT_BASE_PATH=./letsencrypt
CERT_OUTPUT_PATH=./certificates
ACCOUNT_KEY_PATH=./letsencrypt/account.key
LETSENCRYPT_EMAIL=<EMAIL>
LETSENCRYPT_STAGING=false
ACME_DIRECTORY_URL=https://acme-v02.api.letsencrypt.org/directory
CERT_KEY_SIZE=2048
CERT_VALIDITY_DAYS=90

# AWS Route53 DNS验证配置
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1

# 阿里云DNS验证配置
ALICLOUD_ACCESS_KEY=your_alicloud_access_key
ALICLOUD_SECRET_KEY=your_alicloud_secret_key
ALICLOUD_REGION=cn-hangzhou

# Cloudflare DNS验证配置
CLOUDFLARE_EMAIL=<EMAIL>
CLOUDFLARE_API_KEY=your_cloudflare_api_key
# 推荐使用API Token（更安全）
# CLOUDFLARE_API_TOKEN=your_api_token_here

# GoDaddy DNS验证配置
GODADDY_API_KEY=your_godaddy_api_key
GODADDY_API_SECRET=your_godaddy_api_secret
```

## 使用方法

### 1. 独立运行

```bash
python agents/letsencrypt/main.py
```

### 2. 集成到统一代理

```python
from ops_agent import APIAgent

agent = APIAgent()
result = await agent.invoke("为 example.com 签发证书，使用AWS Route53验证")
```

### 3. 直接调用

```python
from agents.letsencrypt.letsencrypt_agent import LetsEncryptAgent

agent = LetsEncryptAgent()
result = await agent.invoke("为 example.com 签发证书，使用AWS Route53验证")
```

## 使用示例

### 智能平台匹配示例

```python
# 搜索平台功能（区分支持状态）
"搜索研发支撑相关平台"      # 显示支持✅和不支持❌的平台及原因
"查找gitlab相关的平台"      # 搜索包含gitlab的平台

# 支持自动签发的平台（Letsencrypt + txt验证）
"为研发支撑嘉兴s3申请证书"  # ✅ 支持：ifs.inhand.online (阿里云DNS)
"给攀雀海外申请证书"        # ✅ 支持：penduline.ai (AWS Route53)
"为国内论坛申请证书"        # ✅ 支持：support.m2mlib.com (阿里云DNS)

# 不支持自动签发的平台（会显示原因）
"为研发支撑序列号测试库申请证书"  # ❌ 不支持：签发机构为Digicert
"给海外论坛申请证书"              # ❌ 不支持：验证方式为file验证

# 智能提示和建议
系统会自动提示不支持的原因，并建议手动申请或联系管理员
```

### 传统证书签发示例

```python
# AWS Route53域名签发（*.inhand.design）
"为 pypi.inhand.design 签发证书，使用AWS Route53验证"

# 阿里云DNS通配符证书（*.inhand.online）
"为 *.inhand.online 签发通配符证书，使用阿里云DNS验证"

# Cloudflare DNS证书签发（*.inhand.com）
"为 resources.inhand.com 申请证书，使用Cloudflare DNS，密钥类型RSA"

# 多域名证书
"为 api.test.com, web.test.com 签发证书，使用AWS验证"
```

### 证书管理示例

```python
# 续期证书
"续期 example.com 的证书"

# 查看所有证书
"查看所有证书状态"

# 检查证书状态
"检查 api.example.com 证书是否即将过期"
```

### 证书导出示例

```python
# 导出PEM格式
"导出 example.com 的证书为PEM格式"

# 导出P12格式
"将 api.example.com 证书导出为P12格式"
```

## 注意事项

1. **DNS验证要求** - 所有证书签发都使用DNS验证方式，需要确保DNS提供商的API凭证已正确配置
2. **域名所有权** - 只能为你拥有DNS控制权的域名签发证书
3. **速率限制** - Let's Encrypt有速率限制，请避免频繁签发同一域名的证书
4. **证书有效期** - Let's Encrypt证书有效期为90天，建议设置自动续期
5. **安全存储** - 证书私钥将安全存储在指定目录，请妥善保管

## 故障排除

### 常见问题

1. **DNS验证失败**
   - 检查DNS提供商凭证是否正确
   - 确认域名解析设置正确
   - 验证API权限是否足够

2. **容器连接失败**
   - 确认Docker容器正在运行
   - 检查容器名称是否正确
   - 验证容器内certbot路径

3. **证书已存在**
   - 可以选择强制更新
   - 或者跳过已存在的证书

4. **速率限制**
   - 等待速率限制重置
   - 使用测试环境进行调试

## 测试

### 基础功能测试

```bash
# 测试代理导入
python -c "from agents.letsencrypt.letsencrypt_agent_simple import SimpleLetsEncryptAgent; print('✅ LetsEncrypt代理可用')"

# 测试统一代理集成
python -c "from ops_agent import APIAgent; agent = APIAgent(); print('✅ 统一代理集成成功')"
```

### 功能测试

```python
from ops_agent import APIAgent
import asyncio

async def test():
    agent = APIAgent()

    # 测试证书状态检查
    result = await agent.invoke("检查 www.baidu.com 的证书状态")
    print(f"状态: {result.status}")
    print(f"结果: {result.result}")

asyncio.run(test())
```

## 文件结构

```
agents/letsencrypt/
├── letsencrypt_agent_fixed.py      # 修复版LetsEncrypt代理（主要实现）
├── letsencrypt_schema.py           # 参数验证模型
├── letsencrypt_prompt.md           # 代理提示词
├── certificate_api_client.py       # 证书API客户端（从CMDB获取平台数据）
├── platform-domains.md             # 平台域名映射表（备用静态文件）
├── 平台域名匹配功能说明.md          # 智能匹配功能详细说明
├── aliyun_dns_script.py           # 阿里云DNS自动化脚本
├── auth_hook.py                   # DNS验证钩子脚本（运行时生成）
├── cleanup_hook.py                # DNS清理钩子脚本（运行时生成）
└── README.md                      # 本文档

项目根目录/
├── data/                          # 数据目录
│   └── certificates/              # 证书输出文件（迁移后位置）
└── letsencrypt/                   # Certbot工作目录
    ├── live/                      # 当前证书链接
    ├── archive/                   # 证书归档
    ├── renewal/                   # 续期配置
    ├── config/                    # DNS配置文件
    └── logs/                      # Certbot日志
```

## 相关文档

- [平台域名匹配功能说明](平台域名匹配功能说明.md) - 智能平台域名匹配功能的详细说明
- [platform-domains.md](platform-domains.md) - 97个企业平台的完整域名映射表

## 支持

如有问题或建议，请联系开发团队。
