# -*- coding: utf-8 -*-

"""
File: tencent_schema.py
Author: HuangJun
Date: 2025/6/17

腾讯企业邮箱管理工具的数据模型定义。
"""

from typing import Dict, Optional, Any
from pydantic import BaseModel, Field, validator
import re

class CreateEmailAccountSchema(BaseModel):
    """创建企业邮箱账号的参数模型"""
    display_name: str = Field(..., description="用户显示名称")
    email: str = Field(..., description="用户邮箱地址")
    password: Optional[str] = Field(None, description="用户初始密码，如果不提供将生成随机密码")
    department: Optional[str] = Field(None, description="用户所属部门")
    
    @validator('email')
    def validate_email(cls, v):
        """验证邮箱格式"""
        if not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', v):
            raise ValueError('邮箱格式不正确')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        if v is not None:
            if len(v) < 8:
                raise ValueError('密码长度不能少于8位')
            if not any(c.isdigit() for c in v):
                raise ValueError('密码必须包含数字')
            if not any(c.isalpha() for c in v):
                raise ValueError('密码必须包含字母')
        return v

class DeleteEmailAccountSchema(BaseModel):
    """删除企业邮箱账号的参数模型"""
    email: str = Field(..., description="用户邮箱地址")
    
    @validator('email')
    def validate_email(cls, v):
        """验证邮箱格式"""
        if not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', v):
            raise ValueError('邮箱格式不正确')
        return v

class GetEmailAccountSchema(BaseModel):
    """获取企业邮箱账号信息的参数模型"""
    email: str = Field(..., description="用户邮箱地址")
    
    @validator('email')
    def validate_email(cls, v):
        """验证邮箱格式"""
        if not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', v):
            raise ValueError('邮箱格式不正确')
        return v

class UpdateEmailAccountSchema(BaseModel):
    """更新企业邮箱账号信息的参数模型"""
    email: str = Field(..., description="用户邮箱地址")
    update_data: Dict[str, Any] = Field(..., description="要更新的用户数据")
    
    @validator('email')
    def validate_email(cls, v):
        """验证邮箱格式"""
        if not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', v):
            raise ValueError('邮箱格式不正确')
        return v
    
    @validator('update_data')
    def validate_update_data(cls, v):
        """验证更新数据"""
        if not v:
            raise ValueError('更新数据不能为空')
        
        # 检查是否包含不允许更新的字段
        forbidden_fields = ['email']
        for field in forbidden_fields:
            if field in v:
                raise ValueError(f'不允许更新字段: {field}')
        return v