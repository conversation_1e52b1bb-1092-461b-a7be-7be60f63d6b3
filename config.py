# -*- coding: utf-8 -*-

"""
OPS Agent 配置文件

统一管理所有代理的配置信息，包括：
- 环境变量配置
- 默认参数设置
- 系统常量定义

Author: AI Assistant & Team
Date: 2025/6/20
"""

import os
from typing import Dict, Any, List

# 项目基础配置
PROJECT_NAME = "OPS Agent"
PROJECT_VERSION = "1.0.0"

# 默认路径配置
DEFAULT_CERT_BASE_PATH = "./letsencrypt"
DEFAULT_CERT_OUTPUT_PATH = "./data/certificates"
PROJECT_DESCRIPTION = "统一运维代理系统"

# 日志配置
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# OpenAI配置
OPENAI_CONFIG = {
    "api_key": os.getenv("OPENAI_API_KEY"),
    "base_url": os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
    "model": os.getenv("OPENAI_MODEL", "gpt-4"),
    "temperature": float(os.getenv("OPENAI_TEMPERATURE", "0.1")),
    "max_tokens": int(os.getenv("OPENAI_MAX_TOKENS", "4000")),
}

# Azure EntraID配置
AZURE_CONFIG = {
    "tenant_id": os.getenv("AZURE_TENANT_ID"),
    "client_id": os.getenv("AZURE_CLIENT_ID"),
    "client_secret": os.getenv("AZURE_CLIENT_SECRET"),
    "authority": os.getenv("AZURE_AUTHORITY", "https://login.microsoftonline.com"),
    "scope": ["https://graph.microsoft.com/.default"],
}

# 腾讯企业邮箱配置
TENCENT_CONFIG = {
    "corp_id": os.getenv("TENCENT_CORP_ID"),
    "corp_secret": os.getenv("TENCENT_CORP_SECRET"),
    "api_base_url": "https://qyapi.weixin.qq.com/cgi-bin",
}

# AWS配置
AWS_CONFIG = {
    "access_key_id": os.getenv("AWS_ACCESS_KEY_ID"),
    "secret_access_key": os.getenv("AWS_SECRET_ACCESS_KEY"),
    "region": os.getenv("AWS_REGION", "us-east-1"),
}

# 阿里云配置
ALICLOUD_CONFIG = {
    "access_key": os.getenv("ALICLOUD_ACCESS_KEY"),
    "secret_key": os.getenv("ALICLOUD_SECRET_KEY"),
    "region": os.getenv("ALICLOUD_REGION", "cn-hangzhou"),
}

# Cloudflare配置
CLOUDFLARE_CONFIG = {
    "email": os.getenv("CLOUDFLARE_EMAIL"),
    "api_key": os.getenv("CLOUDFLARE_API_KEY"),
    "api_token": os.getenv("CLOUDFLARE_API_TOKEN"),  # 新版API Token（推荐）
}

# LetsEncrypt配置
LETSENCRYPT_CONFIG = {
    "email": os.getenv("LETSENCRYPT_EMAIL", "<EMAIL>"),
    "staging": os.getenv("LETSENCRYPT_STAGING", "false").lower() == "true",
    "cert_base_path": os.getenv("CERT_BASE_PATH", "./letsencrypt"),
    "cert_output_path": os.getenv("CERT_OUTPUT_PATH", "./certificates"),
    "key_type": os.getenv("LETSENCRYPT_KEY_TYPE", "rsa"),
    "key_size": int(os.getenv("LETSENCRYPT_KEY_SIZE", "2048")),
}

# 代理路由配置
AGENT_ROUTING = {
    "azure_keywords": ["azure", "entraid", "用户", "组", "入职", "离职"],
    "email_keywords": ["mail", "邮箱", "email"],
    "letsencrypt_keywords": ["证书", "ssl", "https", "域名", "letsencrypt", "certbot", "cloudflare"],
}

# 文件路径配置
PATHS = {
    "logs": "./logs",
    "certificates": "./certificates",
    "letsencrypt": "./letsencrypt",
    "temp": "./temp",
}

# 系统常量
CONSTANTS = {
    "max_retry_attempts": 3,
    "default_timeout": 30,
    "certificate_validity_days": 90,
    "dns_propagation_wait": 60,
}

def get_config(section: str) -> Dict[str, Any]:
    """
    获取指定配置段
    
    Args:
        section: 配置段名称
        
    Returns:
        配置字典
    """
    config_map = {
        "openai": OPENAI_CONFIG,
        "azure": AZURE_CONFIG,
        "tencent": TENCENT_CONFIG,
        "aws": AWS_CONFIG,
        "alicloud": ALICLOUD_CONFIG,
        "cloudflare": CLOUDFLARE_CONFIG,
        "letsencrypt": LETSENCRYPT_CONFIG,
        "routing": AGENT_ROUTING,
        "paths": PATHS,
        "constants": CONSTANTS,
    }
    
    return config_map.get(section, {})

def validate_config() -> Dict[str, bool]:
    """
    验证配置完整性
    
    Returns:
        验证结果字典
    """
    results = {}
    
    # 验证OpenAI配置
    results["openai"] = bool(OPENAI_CONFIG["api_key"])
    
    # 验证Azure配置
    results["azure"] = all([
        AZURE_CONFIG["tenant_id"],
        AZURE_CONFIG["client_id"],
        AZURE_CONFIG["client_secret"]
    ])
    
    # 验证腾讯配置
    results["tencent"] = all([
        TENCENT_CONFIG["corp_id"],
        TENCENT_CONFIG["corp_secret"]
    ])
    
    # 验证AWS配置
    results["aws"] = all([
        AWS_CONFIG["access_key_id"],
        AWS_CONFIG["secret_access_key"]
    ])
    
    # 验证阿里云配置
    results["alicloud"] = all([
        ALICLOUD_CONFIG["access_key"],
        ALICLOUD_CONFIG["secret_key"]
    ])

    # 验证Cloudflare配置
    results["cloudflare"] = bool(
        CLOUDFLARE_CONFIG["email"] and
        (CLOUDFLARE_CONFIG["api_key"] or CLOUDFLARE_CONFIG["api_token"])
    )

    # 验证LetsEncrypt配置
    results["letsencrypt"] = bool(LETSENCRYPT_CONFIG["email"])

    return results

def get_missing_config() -> List[str]:
    """
    获取缺失的配置项
    
    Returns:
        缺失配置项列表
    """
    validation = validate_config()
    missing = []
    
    for service, is_valid in validation.items():
        if not is_valid:
            missing.append(service)
    
    return missing

if __name__ == "__main__":
    # 配置验证示例
    print(f"🔧 {PROJECT_NAME} v{PROJECT_VERSION}")
    print(f"📝 {PROJECT_DESCRIPTION}")
    print("\n📊 配置验证结果:")
    
    validation = validate_config()
    for service, is_valid in validation.items():
        status = "✅" if is_valid else "❌"
        print(f"  {status} {service.upper()}: {'已配置' if is_valid else '未配置'}")
    
    missing = get_missing_config()
    if missing:
        print(f"\n⚠️  缺失配置: {', '.join(missing)}")
        print("请检查 .env 文件中的相关配置项")
    else:
        print("\n🎉 所有配置项都已正确设置！")
