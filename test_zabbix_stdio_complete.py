#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整的ZabbixAgent STDIO测试

测试STDIO MCP通信架构的完整功能，不依赖真实的Zabbix服务器。
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试所有组件的导入"""
    print("=== 测试组件导入 ===")

    try:
        # 测试STDIO客户端导入
        sys.path.append('agents/zabbix')
        from mcp_stdio_client import MCPSTDIOClient
        print("OK MCPSTDIOClient导入成功")

        from zabbix_stdio_client import ZabbixSTDIOClient
        print("OK ZabbixSTDIOClient导入成功")

        from zabbix_agent import ZabbixAgent
        print("OK ZabbixAgent导入成功")

        return True

    except Exception as e:
        print(f"ERROR 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_zabbix_agent_creation():
    """测试ZabbixAgent创建（不启动MCP服务器）"""
    print("\n=== 测试ZabbixAgent创建 ===")

    try:
        sys.path.append('agents/zabbix')
        from zabbix_agent import ZabbixAgent

        # 创建ZabbixAgent，不自动启动MCP服务器
        agent = ZabbixAgent(auto_start_server=False)
        print("OK ZabbixAgent创建成功")

        # 测试工具加载
        tools = asyncio.run(agent.get_tools())
        print(f"OK 工具加载成功，共{len(tools)}个工具:")
        for tool in tools:
            print(f"  - {tool.name}")

        return True

    except Exception as e:
        print(f"ERROR ZabbixAgent创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mcp_stdio_client():
    """测试MCP STDIO客户端基本功能"""
    print("\n=== 测试MCP STDIO客户端 ===")

    try:
        sys.path.append('agents/zabbix')
        from mcp_stdio_client import MCPSTDIOClient

        # 创建一个简单的测试命令
        client = MCPSTDIOClient(['echo', 'test'])
        print("OK MCPSTDIOClient创建成功")

        # 测试基本属性
        print(f"OK 服务器命令: {client.server_command}")
        print(f"OK 运行状态: {client.is_running}")

        return True

    except Exception as e:
        print(f"ERROR MCP STDIO客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_zabbix_stdio_client():
    """测试Zabbix STDIO客户端（不启动服务器）"""
    print("\n=== 测试Zabbix STDIO客户端 ===")

    try:
        sys.path.append('agents/zabbix')
        from zabbix_stdio_client import ZabbixSTDIOClient

        # 创建客户端，不自动启动服务器
        client = ZabbixSTDIOClient(auto_start_server=False)
        print("OK ZabbixSTDIOClient创建成功")

        # 测试方法存在性
        methods = [
            'get_hostgroup_id',
            'get_hosts_by_groupid',
            'get_items_by_hostids',
            'get_history_data',
            'apiinfo_version',
            'hostgroup_get',
            'host_get',
            'item_get',
            'history_get'
        ]

        for method in methods:
            if hasattr(client, method):
                print(f"OK 方法存在: {method}")
            else:
                print(f"ERROR 方法缺失: {method}")
                return False

        return True

    except Exception as e:
        print(f"ERROR Zabbix STDIO客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_zabbix_agent_tools():
    """测试ZabbixAgent工具功能"""
    print("\n=== 测试ZabbixAgent工具功能 ===")

    try:
        sys.path.append('agents/zabbix')
        from zabbix_agent import ZabbixAgent

        # 创建ZabbixAgent
        agent = ZabbixAgent(auto_start_server=False)
        print("OK ZabbixAgent创建成功")

        # 获取工具
        tools = await agent.get_tools()
        print(f"OK 获取到{len(tools)}个工具")

        # 测试每个工具的基本属性
        expected_tools = [
            'get_comprehensive_metrics',
            'get_problem_analysis_data',
            'get_performance_trends',
            'analyze_and_generate_report',
            'get_hostgroup_info'
        ]

        tool_names = [tool.name for tool in tools]

        for expected_tool in expected_tools:
            if expected_tool in tool_names:
                print(f"OK 工具存在: {expected_tool}")
            else:
                print(f"ERROR 工具缺失: {expected_tool}")
                return False

        print("OK 所有预期工具都存在")
        return True

    except Exception as e:
        print(f"ERROR ZabbixAgent工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("ZabbixAgent STDIO架构完整测试")
    print("=" * 60)

    tests = [
        ("组件导入", test_imports),
        ("MCP STDIO客户端", test_mcp_stdio_client),
        ("Zabbix STDIO客户端", test_zabbix_stdio_client),
        ("ZabbixAgent创建", test_zabbix_agent_creation),
        ("ZabbixAgent工具", lambda: asyncio.run(test_zabbix_agent_tools())),
    ]

    results = {}

    for name, test_func in tests:
        try:
            print(f"\n开始测试: {name}")
            results[name] = test_func()
        except Exception as e:
            print(f"ERROR {name} 测试异常: {e}")
            results[name] = False

    print("\n" + "=" * 60)
    print("测试结果总结:")

    all_passed = True
    for name, result in results.items():
        status = "OK 通过" if result else "ERROR 失败"
        print(f"   {name}: {status}")
        if not result:
            all_passed = False

    print("\n" + "=" * 60)

    if all_passed:
        print("OK 所有测试通过！")
        print("\nOK STDIO MCP架构实现完成:")
        print("   - MCPSTDIOClient: JSON-RPC over STDIO通信")
        print("   - ZabbixSTDIOClient: Zabbix API封装")
        print("   - ZabbixAgent: 高阶业务工具")
        print("   - 完整的三层架构设计")
        print("\n下一步:")
        print("   1. 获取有效的Zabbix API Token")
        print("   2. 更新.env文件")
        print("   3. 运行完整功能测试")
        print("\nOK 架构已就绪，可以处理真实的监控查询！")
    else:
        print("ERROR 部分测试失败，请检查上述错误")

    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())