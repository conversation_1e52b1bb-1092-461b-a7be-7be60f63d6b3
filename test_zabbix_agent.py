#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ZabbixAgent 测试脚本

测试ZabbixAgent的基本功能，包括：
1. 类初始化
2. 工具加载
3. API客户端连接测试
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from agents.zabbix.zabbix_agent import ZabbixAgent
    from agents.zabbix.zabbix_api_client import ZabbixAPIClient
    print("✅ ZabbixAgent 导入成功")
except ImportError as e:
    print(f"❌ ZabbixAgent 导入失败: {e}")
    sys.exit(1)


async def test_zabbix_agent():
    """测试ZabbixAgent基本功能"""
    print("\n🔧 开始测试 ZabbixAgent...")

    try:
        # 测试1: 初始化ZabbixAgent
        print("\n1. 测试ZabbixAgent初始化...")
        agent = ZabbixAgent()
        print("✅ ZabbixAgent 初始化成功")

        # 测试2: 获取工具列表
        print("\n2. 测试获取工具列表...")
        tools = await agent.get_tools()
        print(f"✅ 成功加载 {len(tools)} 个工具:")
        for tool in tools:
            print(f"   - {tool.name}: {tool.description}")

        # 测试3: 测试API客户端（不自动启动服务器）
        print("\n3. 测试API客户端初始化（不自动启动）...")
        api_client = ZabbixAPIClient(auto_start_server=False)
        print("✅ ZabbixAPIClient 初始化成功")
        print(f"   服务器URL: {api_client.server_url}")
        print(f"   超时设置: {api_client.timeout}秒")
        print(f"   重试次数: {api_client.max_retries}")
        print(f"   自动启动服务器: {api_client.auto_start_server}")

        # 测试4: 测试自动启动功能
        print("\n4. 测试自动启动MCP服务器功能...")
        try:
            auto_start_client = ZabbixAPIClient(auto_start_server=True)
            print("✅ 自动启动客户端初始化成功")

            # 测试连接
            connection_ok = auto_start_client.test_connection()
            if connection_ok:
                print("✅ 自动启动并连接测试成功")
            else:
                print("⚠️  自动启动连接测试失败（可能是环境配置问题）")

        except Exception as e:
            print(f"⚠️  自动启动测试失败: {str(e)}")
            print("   这可能是因为:")
            print("   - 缺少Zabbix服务器配置")
            print("   - 缺少Python依赖包")
            print("   - MCP服务器启动脚本问题")

        print("\n🎉 ZabbixAgent 基本功能测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_ops_agent_integration():
    """测试OpsAgent集成"""
    print("\n🔧 开始测试 OpsAgent 集成...")

    try:
        from ops_agent import OpsAgent
        print("✅ OpsAgent 导入成功")

        # 初始化OpsAgent
        ops = OpsAgent()
        print("✅ OpsAgent 初始化成功")

        # 获取工具列表
        tools = await ops.get_tools()
        print(f"✅ OpsAgent 成功加载 {len(tools)} 个工具:")
        for tool in tools:
            print(f"   - {tool.name}")

        # 检查是否包含Zabbix工具
        zabbix_tool_found = any(tool.name == "call_zabbix" for tool in tools)
        if zabbix_tool_found:
            print("✅ 找到 call_zabbix 工具，集成成功")
        else:
            print("❌ 未找到 call_zabbix 工具")
            return False

        print("\n🎉 OpsAgent 集成测试完成！")
        return True

    except Exception as e:
        print(f"❌ OpsAgent 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 ZabbixAgent 功能测试开始")
    print("=" * 50)

    # 测试ZabbixAgent基本功能
    test1_result = await test_zabbix_agent()

    # 测试OpsAgent集成
    test2_result = await test_ops_agent_integration()

    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   ZabbixAgent基本功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   OpsAgent集成测试: {'✅ 通过' if test2_result else '❌ 失败'}")

    if test1_result and test2_result:
        print("\n🎉 所有测试通过！ZabbixAgent 已成功集成到 ops-agent 系统中。")
        print("\n📝 使用说明:")
        print("   1. 启动 zabbix-mcp-server 服务")
        print("   2. 配置环境变量 ZABBIX_MCP_SERVER_URL")
        print("   3. 运行 python ops_agent.py")
        print("   4. 输入监控分析相关的查询，如：")
        print("      - '分析Web服务器主机组的性能状况'")
        print("      - '获取数据库服务器组最近24小时的监控报告'")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)