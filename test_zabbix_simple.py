#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Zabbix简化客户端
"""

import sys
sys.path.append('agents/zabbix')

def test_zabbix_simple_client():
    """测试ZabbixSimpleClient"""
    try:
        from zabbix_simple_client import ZabbixSimpleClient

        print('创建ZabbixSimpleClient...')
        client = ZabbixSimpleClient()

        print('测试连接...')
        if client.test_connection():
            print('✓ 连接成功')

            print('获取API版本...')
            version = client.get_api_version()
            print(f'✓ API版本: {version}')

            print('测试获取主机组...')
            hostgroups = client.hostgroup_get(output=['groupid', 'name'], limit=3)
            print(f'✓ 获取到{len(hostgroups)}个主机组:')
            for group in hostgroups:
                print(f'  - {group["name"]} (ID: {group["groupid"]})')

            print('测试辅助方法...')
            group_id = client.get_hostgroup_id('Linux servers')
            if group_id:
                print(f'✓ Linux servers主机组ID: {group_id}')
            else:
                print('✗ 未找到Linux servers主机组')

        else:
            print('✗ 连接失败')

    except Exception as e:
        print(f'✗ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_zabbix_simple_client()