#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix MCP客户端

基于官方MCP Python SDK实现的Zabbix客户端，用于连接zabbix-mcp-server。
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from utils.logging_config import logger

# 导入官方MCP SDK
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


class ZabbixMCPClient:
    """
    基于官方MCP SDK的Zabbix客户端

    使用官方MCP Python SDK连接zabbix-mcp-server，提供可靠的MCP通信。
    """

    def __init__(self):
        """初始化Zabbix MCP客户端"""
        self.mcp_server_dir = Path(__file__).parent.parent.parent / "mcp-server" / "zabbix-mcp-server"
        logger.info("初始化Zabbix MCP客户端")

    async def _execute_with_session(self, operation):
        """使用MCP会话执行操作"""
        # 配置服务器参数
        server_params = StdioServerParameters(
            command="uv",
            args=["run", "python", "src/zabbix_mcp_server.py"],
            cwd=str(self.mcp_server_dir)
        )

        # 使用官方SDK的方式连接
        async with stdio_client(server_params) as (read_stream, write_stream):
            async with ClientSession(read_stream, write_stream) as session:
                # 初始化会话
                await session.initialize()

                # 执行操作
                return await operation(session)

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Any:
        """调用MCP工具"""
        if arguments is None:
            arguments = {}

        async def operation(session):
            logger.debug(f"调用MCP工具: {tool_name}, 参数: {arguments}")

            # 使用官方SDK调用工具
            result = await session.call_tool(tool_name, arguments)

            # 处理结果
            if hasattr(result, 'content') and result.content:
                # 提取文本内容
                content_text = ""
                for item in result.content:
                    if hasattr(item, 'text'):
                        content_text += item.text

                # 尝试解析JSON
                if content_text:
                    try:
                        import json
                        return json.loads(content_text)
                    except json.JSONDecodeError:
                        return {"result": content_text}

            return {"result": "No content"}

        try:
            return await self._execute_with_session(operation)
        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {e}")
            raise

    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取MCP服务器的可用工具列表"""
        try:
            server_params = StdioServerParameters(
                command="uv",
                args=["run", "--directory", "D:/fanpeng/zabbix-mcp-server", "python", "src/zabbix_mcp_server.py"],
                env={
                    "ZABBIX_URL": "http://zabbix.inhand.online/",
                    "ZABBIX_TOKEN": os.getenv("ZABBIX_TOKEN", ""),
                    "READ_ONLY": "true"
                }
            )

            async with stdio_client(server_params) as (read_stream, write_stream):
                async with ClientSession(read_stream, write_stream) as session:
                    await session.initialize()

                    # 获取可用工具列表
                    tools = await session.list_tools()
                    return tools.tools if hasattr(tools, 'tools') else []

        except Exception as e:
            logger.error(f"获取MCP工具列表失败: {e}")
            return []

    # 以下是ZabbixAgent需要的方法

    async def get_hostgroup_id(self, hostgroup_name: str) -> Optional[str]:
        """根据主机组名称获取主机组ID"""
        try:
            result = await self.call_tool("hostgroup_get", {
                "output": ["groupid", "name"],
                "filter": {"name": [hostgroup_name]}
            })

            if isinstance(result, list) and result:
                return result[0].get("groupid")
            return None
        except Exception as e:
            logger.error(f"获取主机组ID失败: {e}")
            return None

    async def get_host_ids_in_group(self, groupid: str) -> List[str]:
        """根据主机组ID获取主机ID列表"""
        try:
            result = await self.call_tool("host_get", {
                "output": ["hostid"],
                "groupids": [groupid]
            })

            if isinstance(result, list):
                return [host.get("hostid") for host in result if host.get("hostid")]
            return []
        except Exception as e:
            logger.error(f"获取主机ID列表失败: {e}")
            return []

    async def get_item_ids(self, hostids: List[str], keys: List[str]) -> Dict[str, List[str]]:
        """根据主机ID和监控项key获取监控项ID字典"""
        try:
            result = await self.call_tool("item_get", {
                "output": ["itemid", "key_"],
                "hostids": hostids,
                "filter": {"key_": keys}
            })

            item_dict = {}
            if isinstance(result, list):
                for item in result:
                    key = item.get("key_")
                    itemid = item.get("itemid")
                    if key and itemid:
                        if key not in item_dict:
                            item_dict[key] = []
                        item_dict[key].append(itemid)

            return item_dict
        except Exception as e:
            logger.error(f"获取监控项ID失败: {e}")
            return {}

    async def get_history(self, itemids: List[str], time_from: int, time_till: int = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            params = {
                "output": "extend",
                "itemids": itemids,
                "time_from": time_from,
                "limit": limit,
                "sortfield": "clock",
                "sortorder": "DESC"
            }

            if time_till:
                params["time_till"] = time_till

            result = await self.call_tool("history_get", params)

            if isinstance(result, list):
                return result
            return []
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []

    async def get_trends(self, itemids: List[str], time_from: int, time_till: int = None) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        try:
            params = {
                "output": "extend",
                "itemids": itemids,
                "time_from": time_from
            }

            if time_till:
                params["time_till"] = time_till

            result = await self.call_tool("trend_get", params)

            if isinstance(result, list):
                return result
            return []
        except Exception as e:
            logger.error(f"获取趋势数据失败: {e}")
            return []

    async def get_problems(self, groupids: List[str] = None, severities: List[int] = None,
                          time_from: int = None, time_till: int = None, recent: bool = True,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """获取问题数据"""
        try:
            params = {
                "output": "extend",
                "limit": limit,
                "sortfield": "eventid",
                "sortorder": "DESC"
            }

            if groupids:
                params["groupids"] = groupids
            if severities:
                params["severities"] = severities
            if time_from:
                params["time_from"] = time_from
            if time_till:
                params["time_till"] = time_till
            if recent:
                params["recent"] = "true"

            result = await self.call_tool("problem_get", params)

            if isinstance(result, list):
                return result
            return []
        except Exception as e:
            logger.error(f"获取问题数据失败: {e}")
            return []

    async def get_triggers(self, groupids: List[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取触发器数据"""
        try:
            params = {
                "output": "extend",
                "limit": limit
            }

            if groupids:
                params["groupids"] = groupids

            result = await self.call_tool("trigger_get", params)

            if isinstance(result, list):
                return result
            return []
        except Exception as e:
            logger.error(f"获取触发器数据失败: {e}")
            return []