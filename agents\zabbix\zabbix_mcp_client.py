#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix MCP客户端

使用官方MCP Python SDK连接zabbix-mcp-server，提供可靠的MCP通信。
"""

import os
import asyncio
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional, List
from utils.logging_config import logger, log_tool_execution

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
except ImportError:
    logger.error("MCP SDK未安装，请运行: uv pip install mcp")
    raise


class ZabbixMCPClient:
    """
    Zabbix MCP客户端

    使用官方MCP Python SDK连接zabbix-mcp-server。
    """

    def __init__(self, auto_start_server: bool = True):
        """
        初始化Zabbix MCP客户端

        Args:
            auto_start_server: 是否自动启动MCP服务器
        """
        self.auto_start_server = auto_start_server
        self.session: Optional[ClientSession] = None
        self.server_process: Optional[subprocess.Popen] = None

        # MCP服务器配置
        self.mcp_server_dir = Path(__file__).parent.parent.parent / "mcp-server" / "zabbix-mcp-server"

        logger.info("初始化Zabbix MCP客户端")

    async def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            # 配置服务器参数
            server_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "src/zabbix_mcp_server.py"],
                cwd=str(self.mcp_server_dir)
            )

            # 创建STDIO客户端连接（这会自动启动服务器）
            async with stdio_client(server_params) as (read_stream, write_stream):
                # 创建客户端会话
                async with ClientSession(read_stream, write_stream) as session:
                    # 初始化会话
                    await session.initialize()

                    # 保存会话引用
                    self.session = session

                    logger.info("MCP客户端连接成功")
                    return True

        except Exception as e:
            logger.error(f"MCP客户端连接失败: {e}")
            return False

    async def _start_server(self):
        """启动MCP服务器（如果需要）"""
        # 注意：在使用stdio_client时，服务器会自动启动
        # 这里只是为了兼容性保留
        pass

    async def disconnect(self):
        """断开连接"""
        if self.session:
            await self.session.close()
            self.session = None

        if self.server_process:
            self.server_process.terminate()
            self.server_process = None

    async def list_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        if not self.session:
            if not await self.connect():
                raise Exception("无法连接到MCP服务器")

        try:
            result = await self.session.list_tools()
            return [tool.model_dump() for tool in result.tools]
        except Exception as e:
            logger.error(f"获取工具列表失败: {e}")
            raise

    @log_tool_execution(tool_name="call_tool")
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        调用MCP工具

        Args:
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            工具执行结果
        """
        if not self.session:
            if not await self.connect():
                raise Exception("无法连接到MCP服务器")

        if arguments is None:
            arguments = {}

        try:
            result = await self.session.call_tool(tool_name, arguments)

            # 处理结果
            if result.isError:
                raise Exception(f"工具调用错误: {result.content}")

            # 返回内容
            content = []
            for item in result.content:
                if hasattr(item, 'text'):
                    content.append({"type": "text", "text": item.text})
                elif hasattr(item, 'data'):
                    content.append({"type": "data", "data": item.data})

            return {"content": content}

        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {e}")
            raise

    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            if not self.session:
                if not await self.connect():
                    return False

            # 尝试获取工具列表
            tools = await self.list_tools()
            return len(tools) > 0

        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    # 以下是Zabbix API工具的封装方法

    async def apiinfo_version(self) -> Dict[str, Any]:
        """获取API版本信息"""
        return await self.call_tool("apiinfo_version")

    async def hostgroup_get(self, **kwargs) -> Dict[str, Any]:
        """获取主机组"""
        return await self.call_tool("hostgroup_get", kwargs)

    async def host_get(self, **kwargs) -> Dict[str, Any]:
        """获取主机"""
        return await self.call_tool("host_get", kwargs)

    async def item_get(self, **kwargs) -> Dict[str, Any]:
        """获取监控项"""
        return await self.call_tool("item_get", kwargs)

    async def history_get(self, **kwargs) -> Dict[str, Any]:
        """获取历史数据"""
        return await self.call_tool("history_get", kwargs)

    async def trend_get(self, **kwargs) -> Dict[str, Any]:
        """获取趋势数据"""
        return await self.call_tool("trend_get", kwargs)

    async def problem_get(self, **kwargs) -> Dict[str, Any]:
        """获取问题"""
        return await self.call_tool("problem_get", kwargs)

    async def event_get(self, **kwargs) -> Dict[str, Any]:
        """获取事件"""
        return await self.call_tool("event_get", kwargs)

    async def trigger_get(self, **kwargs) -> Dict[str, Any]:
        """获取触发器"""
        return await self.call_tool("trigger_get", kwargs)

    # 添加ZabbixAgent需要的辅助方法

    async def get_hostgroup_id(self, hostgroup_name: str) -> Optional[str]:
        """根据主机组名称获取主机组ID"""
        try:
            result = await self.hostgroup_get(
                filter={"name": hostgroup_name},
                output=["groupid"]
            )

            # 解析MCP响应
            if result and "content" in result:
                content = result["content"]
                if isinstance(content, list) and len(content) > 0:
                    # 解析JSON数据
                    import json
                    data = json.loads(content[0].get("text", "[]"))
                    if data and len(data) > 0:
                        return data[0].get("groupid")

            return None

        except Exception as e:
            logger.error(f"获取主机组ID失败: {e}")
            return None

    async def get_hosts_by_groupid(self, groupid: str) -> List[Dict[str, Any]]:
        """根据主机组ID获取主机列表"""
        try:
            result = await self.host_get(
                groupids=[groupid],
                output=["hostid", "host", "name", "status"]
            )

            # 解析MCP响应
            if result and "content" in result:
                content = result["content"]
                if isinstance(content, list) and len(content) > 0:
                    import json
                    data = json.loads(content[0].get("text", "[]"))
                    return data if isinstance(data, list) else []

            return []

        except Exception as e:
            logger.error(f"获取主机列表失败: {e}")
            return []