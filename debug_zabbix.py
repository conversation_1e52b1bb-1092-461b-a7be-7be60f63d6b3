#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix调试脚本 - 逐步测试所有组件
"""

import os
import sys
import time
import subprocess
import requests
from pathlib import Path

def test_dependencies():
    """测试依赖包"""
    print("=== 测试Python依赖包 ===")

    dependencies = [
        'requests',
        'fastmcp',
        'dotenv',
        'zabbix_utils'
    ]

    for dep in dependencies:
        try:
            __import__(dep)
            print(f"OK {dep}")
        except ImportError:
            print(f"ERROR {dep} - 缺失")
            return False

    return True

def test_env_config():
    """测试环境配置"""
    print("\n=== 测试环境配置 ===")

    env_file = Path("mcp-server/zabbix-mcp-server/config/.env")
    if not env_file.exists():
        print(f"ERROR 配置文件不存在: {env_file}")
        return False

    print(f"OK 配置文件存在: {env_file}")

    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)

        zabbix_url = os.getenv("ZABBIX_URL")
        zabbix_token = os.getenv("ZABBIX_TOKEN")

        if zabbix_url:
            print(f"OK ZABBIX_URL: {zabbix_url}")
        else:
            print("ERROR ZABBIX_URL 未配置")
            return False

        if zabbix_token:
            print(f"OK ZABBIX_TOKEN: {zabbix_token[:10]}...")
        else:
            print("ERROR ZABBIX_TOKEN 未配置")
            return False

    except Exception as e:
        print(f"ERROR 加载环境变量失败: {e}")
        return False

    return True

def test_zabbix_connection():
    """测试Zabbix服务器连接"""
    print("\n=== 测试Zabbix服务器连接 ===")

    try:
        from dotenv import load_dotenv
        load_dotenv("mcp-server/zabbix-mcp-server/config/.env")

        zabbix_url = os.getenv("ZABBIX_URL")
        zabbix_token = os.getenv("ZABBIX_TOKEN")

        # 构建API URL
        if not zabbix_url.endswith('/'):
            zabbix_url += '/'
        api_url = zabbix_url + "api_jsonrpc.php"

        print(f"连接到: {api_url}")

        # 测试API调用
        payload = {
            "jsonrpc": "2.0",
            "method": "apiinfo.version",
            "params": {},
            "auth": zabbix_token,
            "id": 1
        }

        response = requests.post(api_url, json=payload, timeout=10)
        response.raise_for_status()

        result = response.json()
        if 'result' in result:
            print(f"OK Zabbix API版本: {result['result']}")
            return True
        else:
            print(f"ERROR API调用失败: {result}")
            return False

    except Exception as e:
        print(f"ERROR 连接失败: {e}")
        return False

def test_mcp_server_start():
    """测试MCP服务器启动"""
    print("\n=== 测试MCP服务器启动 ===")

    script_path = Path("mcp-server/zabbix-mcp-server/scripts/start_server.py")
    if not script_path.exists():
        print(f"ERROR 启动脚本不存在: {script_path}")
        return False

    print(f"OK 启动脚本存在: {script_path}")

    try:
        print("启动MCP服务器...")
        process = subprocess.Popen(
            ["python", str(script_path)],
            cwd="mcp-server/zabbix-mcp-server",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 等待几秒钟
        time.sleep(5)

        # 检查进程状态
        if process.poll() is None:
            print("OK MCP服务器进程正在运行")

            # 测试健康检查
            try:
                response = requests.get("http://localhost:8000/health", timeout=5)
                if response.status_code == 200:
                    print("OK MCP服务器健康检查通过")
                    success = True
                else:
                    print(f"ERROR 健康检查失败: {response.status_code}")
                    success = False
            except:
                print("ERROR 无法连接到MCP服务器")
                success = False

            # 停止进程
            process.terminate()
            process.wait()
            return success
        else:
            stdout, stderr = process.communicate()
            print(f"ERROR MCP服务器启动失败")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False

    except Exception as e:
        print(f"ERROR 启动MCP服务器时发生错误: {e}")
        return False

def main():
    """主测试函数"""
    print("Zabbix完整功能测试")
    print("=" * 50)

    tests = [
        ("依赖包", test_dependencies),
        ("环境配置", test_env_config),
        ("Zabbix连接", test_zabbix_connection),
        ("MCP服务器", test_mcp_server_start),
    ]

    results = {}

    for name, test_func in tests:
        try:
            results[name] = test_func()
        except Exception as e:
            print(f"ERROR {name} 测试异常: {e}")
            results[name] = False

    print("\n" + "=" * 50)
    print("测试结果总结:")

    all_passed = True
    for name, result in results.items():
        status = "OK 通过" if result else "ERROR 失败"
        print(f"   {name}: {status}")
        if not result:
            all_passed = False

    if all_passed:
        print("\nOK 所有测试通过！可以正常使用ZabbixAgent")
        print("\n下一步:")
        print("python start_ops_agent_with_zabbix.py")
    else:
        print("\nERROR 部分测试失败，请根据上述错误信息进行修复")

    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())