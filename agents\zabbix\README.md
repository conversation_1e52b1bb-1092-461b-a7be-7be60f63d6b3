# ZabbixAgent - Zabbix监控分析代理

## 概述

ZabbixAgent是ops-agent框架中的Zabbix监控分析模块，提供全面的Zabbix监控数据获取、分析和智能报告生成功能。

## 功能特性

### 🔍 核心功能
- **综合监控指标获取**: 支持CPU、内存、磁盘、网络等多维度性能指标
- **问题和告警分析**: 实时问题监控、严重级别分析、根因诊断
- **性能趋势分析**: 长期趋势预测、容量规划建议
- **智能报告生成**: AI驱动的专业监控分析报告

### 🛠️ 技术架构

采用三层架构设计：

```
┌─────────────────────────────────────────────────────────┐
│                   AI分析层                                │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │  性能分析报告    │ │  问题根因分析    │ │  容量规划    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   逻辑层 (ZabbixAgent)                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │  综合指标获取    │ │  问题数据获取    │ │  趋势分析    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                数据层 (API Client)                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │  zabbix-mcp     │ │  HTTP通信封装    │ │  错误处理    │ │
│  │  40个工具       │ │                 │ │             │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 文件结构

```
agents/zabbix/
├── __init__.py                  # 包初始化文件
├── zabbix_agent.py             # 主Agent类
├── zabbix_api_client.py        # API客户端
├── zabbix_schema.py            # 参数Schema定义
├── zabbix_prompt.md            # 系统提示词
└── README.md                   # 本文档
```

## 核心组件

### 1. ZabbixAgent (主代理类)

提供5个高阶工具供LLM调用：

- **get_comprehensive_metrics**: 获取全面的系统性能指标
- **get_problem_analysis_data**: 获取问题和告警数据
- **get_performance_trends**: 获取性能趋势数据
- **analyze_and_generate_report**: AI分析生成报告
- **get_hostgroup_info**: 获取主机组基础信息

### 2. ZabbixAPIClient (API客户端)

封装与zabbix-mcp-server的HTTP通信：

- 支持重试机制和错误处理
- 提供40个底层API工具的封装
- 统一的响应格式处理

### 3. Schema定义

使用Pydantic定义严格的参数验证：

- `GetComprehensiveMetricsSchema`: 综合指标获取参数
- `GetProblemAnalysisDataSchema`: 问题分析参数
- `GetPerformanceTrendsSchema`: 趋势分析参数
- `AnalyzeAndGenerateReportSchema`: 报告生成参数

## 环境配置

### 必需环境变量

```bash
# zabbix-mcp-server服务地址
ZABBIX_MCP_SERVER_URL=http://localhost:8000

# 可选配置
ZABBIX_API_TIMEOUT=30              # API超时时间（秒）
ZABBIX_API_MAX_RETRIES=3           # 最大重试次数
ZABBIX_API_RETRY_DELAY=1.0         # 重试延迟（秒）
```

### zabbix-mcp-server配置

#### 自动启动模式（推荐）

ZabbixAgent默认会自动启动zabbix-mcp-server，只需配置环境变量：

```bash
# 1. 复制配置文件
copy "mcp-server\zabbix-mcp-server\config\.env.example" "mcp-server\zabbix-mcp-server\config\.env"

# 2. 编辑 .env 文件，配置以下变量：
ZABBIX_URL=http://your-zabbix-server/api_jsonrpc.php
ZABBIX_TOKEN=your-api-token
# 或者使用用户名密码
# ZABBIX_USER=your-username
# ZABBIX_PASSWORD=your-password

# 3. 运行ZabbixAgent（会自动启动MCP服务器）
python start_ops_agent_with_zabbix.py
```

#### 手动启动模式

如果需要手动控制zabbix-mcp-server：

```bash
# 进入zabbix-mcp-server目录
cd mcp-server/zabbix-mcp-server

# 启动服务
python scripts/start_server.py
```

## 使用示例

### 1. 快速启动（推荐）

使用提供的启动脚本，自动处理所有配置和启动：

```bash
# 1. 配置环境变量
copy "mcp-server\zabbix-mcp-server\config\.env.example" "mcp-server\zabbix-mcp-server\config\.env"
# 编辑 .env 文件，配置 ZABBIX_URL 和认证信息

# 2. 启动（自动启动zabbix-mcp-server）
python start_ops_agent_with_zabbix.py
```

### 2. 基本使用

```python
from agents.zabbix import ZabbixAgent

# 初始化代理（自动启动MCP服务器）
agent = ZabbixAgent()

# 执行监控分析任务
result = await agent.invoke("分析Web服务器主机组的性能状况")
print(result.result)
```

### 3. 通过OpsAgent使用

```python
from ops_agent import OpsAgent

# 初始化统一代理（自动启动zabbix-mcp-server）
ops = OpsAgent()

# 执行Zabbix相关任务
result = await ops.invoke("获取数据库服务器组最近24小时的监控报告")
print(result.result)
```

### 4. 手动控制MCP服务器

```python
from agents.zabbix import ZabbixAgent

# 不自动启动MCP服务器
agent = ZabbixAgent(auto_start_server=False)

# 手动启动
if agent.api_client.server_manager:
    agent.api_client.server_manager.start_server()
```

### 3. 支持的查询示例

- "分析Web服务器主机组的性能状况"
- "获取数据库服务器组最近24小时的监控报告"
- "检查Linux服务器组的CPU和内存使用情况"
- "生成应用服务器组的容量规划报告"
- "分析最近一周的系统性能趋势"

## 监控指标支持

### CPU指标
- `system.cpu.util`: CPU使用率
- `system.cpu.load[percpu,avg1]`: 1分钟平均负载
- `system.cpu.load[percpu,avg5]`: 5分钟平均负载

### 内存指标
- `vm.memory.size[pavailable]`: 可用内存百分比
- `vm.memory.util`: 内存使用率
- `vm.memory.size[available]`: 可用内存大小

### 磁盘指标
- `vfs.fs.size[/,pfree]`: 根分区可用空间百分比
- `vfs.fs.size[/,pused]`: 根分区已用空间百分比
- `vfs.fs.inode[/,pfree]`: 根分区可用inode百分比

### 网络指标
- `net.if.in[eth0]`: 网络入流量
- `net.if.out[eth0]`: 网络出流量
- `net.if.in[eth0,bytes]`: 网络入字节数
- `net.if.out[eth0,bytes]`: 网络出字节数

### 系统指标
- `system.uptime`: 系统运行时间
- `proc.num[,,run]`: 运行中的进程数
- `system.users.num`: 登录用户数

## 报告格式

生成的分析报告包含以下标准结构：

```markdown
# 系统监控分析报告

## 📊 总体概览
- 监控时间范围
- 主机组信息
- 关键指标摘要

## 🔍 详细分析
### CPU性能分析
### 内存性能分析
### 存储性能分析
### 网络性能分析

## ⚠️ 问题与告警
- 当前活跃问题
- 问题严重级别分布
- 根因分析

## 📈 趋势预测
- 资源使用趋势
- 容量预测

## 💡 优化建议
- 立即处理事项
- 优化建议
- 容量规划

## 📋 行动计划
- 紧急处理事项
- 短期优化任务
- 长期规划建议
```

## 故障排除

### 常见问题

1. **连接zabbix-mcp-server失败**
   - 检查ZABBIX_MCP_SERVER_URL配置
   - 确认zabbix-mcp-server服务正在运行
   - 检查网络连接

2. **获取监控数据失败**
   - 检查Zabbix服务器连接
   - 验证Zabbix用户权限
   - 确认主机组和监控项存在

3. **报告生成失败**
   - 检查LLM服务可用性
   - 验证输入数据格式
   - 查看详细错误日志

### 调试模式

启用详细日志：

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## 扩展开发

### 添加新的监控指标

1. 在`ZabbixAgent.__init__`中的`metric_keys`字典添加新指标
2. 更新相关Schema定义
3. 测试新指标的数据获取

### 添加新的分析工具

1. 在`ZabbixAgent`类中添加新的工具方法
2. 定义对应的Schema
3. 在`get_tools`方法中注册新工具
4. 更新系统提示词

## 版本信息

- **版本**: 1.0.0
- **作者**: AI Assistant
- **日期**: 2025/7/9
- **依赖**: ops-agent框架, zabbix-mcp-server

## 许可证

本项目遵循与ops-agent主项目相同的许可证。