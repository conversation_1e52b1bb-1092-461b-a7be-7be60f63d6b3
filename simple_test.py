#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的ZabbixAgent测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("Testing imports...")

    try:
        # 直接导入，避免agents.__init__.py的依赖问题
        import sys
        sys.path.append('agents/zabbix')
        from zabbix_api_client import ZabbixMCPServerManager, ZabbixAPIClient
        print("OK ZabbixAPIClient import successful")

        # 测试MCP服务器管理器
        manager = ZabbixMCPServerManager()
        print(f"OK MCP Server Manager initialized")
        print(f"  Server URL: {manager.server_url}")
        print(f"  MCP Server Dir: {manager.mcp_server_dir}")
        print(f"  Start Script: {manager.start_script}")
        print(f"  Script exists: {manager.start_script.exists()}")

        # 测试API客户端（不自动启动）
        client = ZabbixAPIClient(auto_start_server=False)
        print(f"OK API Client initialized (no auto-start)")
        print(f"  Server URL: {client.server_url}")
        print(f"  Auto start: {client.auto_start_server}")

        return True

    except Exception as e:
        print(f"ERROR Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== ZabbixAgent Simple Test ===")

    if test_imports():
        print("\nOK All tests passed!")
        print("\nNext steps:")
        print("1. Configure mcp-server/zabbix-mcp-server/config/.env")
        print("2. Run: python start_ops_agent_with_zabbix.py")
        return 0
    else:
        print("\nERROR Tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())