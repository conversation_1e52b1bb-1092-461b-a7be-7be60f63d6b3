#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix STDIO API客户端

使用STDIO模式与zabbix-mcp-server通信的客户端实现。
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from utils.logging_config import logger, log_tool_execution
from mcp_stdio_client import MCPSTDIOClient


class ZabbixSTDIOClient:
    """
    Zabbix STDIO API客户端

    使用STDIO模式与zabbix-mcp-server通信，提供40个Zabbix API工具的访问接口。
    """

    def __init__(self, auto_start_server: bool = True):
        """
        初始化Zabbix STDIO客户端

        Args:
            auto_start_server: 是否自动启动MCP服务器
        """
        self.auto_start_server = auto_start_server
        self.mcp_client: Optional[MCPSTDIOClient] = None
        self.timeout = int(os.getenv("ZABBIX_API_TIMEOUT", "30"))
        self.max_retries = int(os.getenv("ZABBIX_API_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("ZABBIX_API_RETRY_DELAY", "1.0"))

        # MCP服务器配置
        self.mcp_server_dir = Path(__file__).parent.parent.parent / "mcp-server" / "zabbix-mcp-server"

        logger.info("初始化Zabbix STDIO客户端")

        if auto_start_server:
            self._ensure_server_running()

    def _ensure_server_running(self) -> bool:
        """确保MCP服务器正在运行"""
        if self.mcp_client and self.mcp_client.is_healthy():
            return True

        try:
            # 创建STDIO客户端
            server_command = [
                "uv", "run", "python", "src/zabbix_mcp_server.py"
            ]

            self.mcp_client = MCPSTDIOClient(
                server_command=server_command,
                server_cwd=str(self.mcp_server_dir)
            )

            # 启动服务器
            if self.mcp_client.start_server():
                logger.info("MCP服务器启动成功")
                return True
            else:
                logger.error("MCP服务器启动失败")
                return False

        except Exception as e:
            logger.error(f"启动MCP服务器时发生错误: {e}")
            return False

    def stop_server(self):
        """停止MCP服务器"""
        if self.mcp_client:
            self.mcp_client.stop_server()
            self.mcp_client = None

    @log_tool_execution(tool_name="_call_tool")
    def _call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        调用MCP工具

        Args:
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            工具执行结果
        """
        if not self.mcp_client:
            if not self._ensure_server_running():
                raise Exception("无法连接到MCP服务器")

        try:
            result = self.mcp_client.call_tool(tool_name, arguments or {})
            if result and "error" not in result:
                return result
            else:
                error_msg = result.get("error", "未知错误") if result else "调用失败"
                raise Exception(f"工具调用失败: {error_msg}")

        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {e}")
            raise

    def test_connection(self) -> bool:
        """测试连接"""
        try:
            if not self.mcp_client:
                if not self._ensure_server_running():
                    return False

            # 尝试获取工具列表
            tools = self.mcp_client.list_tools()
            return tools is not None

        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    def get_api_version(self) -> str:
        """获取Zabbix API版本"""
        result = self._call_tool("apiinfo_version")
        return result.get("content", [{}])[0].get("text", "Unknown")

    # 以下是40个Zabbix API工具的封装方法

    def apiinfo_version(self) -> Dict[str, Any]:
        """获取API版本信息"""
        return self._call_tool("apiinfo_version")

    def hostgroup_get(self, **kwargs) -> Dict[str, Any]:
        """获取主机组"""
        return self._call_tool("hostgroup_get", kwargs)

    def host_get(self, **kwargs) -> Dict[str, Any]:
        """获取主机"""
        return self._call_tool("host_get", kwargs)

    def item_get(self, **kwargs) -> Dict[str, Any]:
        """获取监控项"""
        return self._call_tool("item_get", kwargs)

    def history_get(self, **kwargs) -> Dict[str, Any]:
        """获取历史数据"""
        return self._call_tool("history_get", kwargs)

    def trend_get(self, **kwargs) -> Dict[str, Any]:
        """获取趋势数据"""
        return self._call_tool("trend_get", kwargs)

    def problem_get(self, **kwargs) -> Dict[str, Any]:
        """获取问题"""
        return self._call_tool("problem_get", kwargs)

    def event_get(self, **kwargs) -> Dict[str, Any]:
        """获取事件"""
        return self._call_tool("event_get", kwargs)

    def trigger_get(self, **kwargs) -> Dict[str, Any]:
        """获取触发器"""
        return self._call_tool("trigger_get", kwargs)