#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix STDIO API客户端

使用STDIO模式与zabbix-mcp-server通信的客户端实现。
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from utils.logging_config import logger, log_tool_execution
from .mcp_stdio_client import MCPSTDIOClient


class ZabbixSTDIOClient:
    """
    Zabbix STDIO API客户端

    使用STDIO模式与zabbix-mcp-server通信，提供40个Zabbix API工具的访问接口。
    """

    def __init__(self, auto_start_server: bool = True):
        """
        初始化Zabbix STDIO客户端

        Args:
            auto_start_server: 是否自动启动MCP服务器
        """
        self.auto_start_server = auto_start_server
        self.mcp_client: Optional[MCPSTDIOClient] = None
        self.timeout = int(os.getenv("ZABBIX_API_TIMEOUT", "30"))
        self.max_retries = int(os.getenv("ZABBIX_API_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("ZABBIX_API_RETRY_DELAY", "1.0"))

        # MCP服务器配置
        self.mcp_server_dir = Path(__file__).parent.parent.parent / "mcp-server" / "zabbix-mcp-server"

        logger.info("初始化Zabbix STDIO客户端")

        if auto_start_server:
            self._ensure_server_running()

    def _ensure_server_running(self) -> bool:
        """确保MCP服务器正在运行"""
        if self.mcp_client and self.mcp_client.is_healthy():
            return True

        try:
            # 创建STDIO客户端
            server_command = [
                "uv", "run", "python", "src/zabbix_mcp_server.py"
            ]

            self.mcp_client = MCPSTDIOClient(
                server_command=server_command,
                server_cwd=str(self.mcp_server_dir)
            )

            # 启动服务器
            if self.mcp_client.start_server():
                logger.info("MCP服务器启动成功")
                return True
            else:
                logger.error("MCP服务器启动失败")
                return False

        except Exception as e:
            logger.error(f"启动MCP服务器时发生错误: {e}")
            return False

    def stop_server(self):
        """停止MCP服务器"""
        if self.mcp_client:
            self.mcp_client.stop_server()
            self.mcp_client = None

    @log_tool_execution(tool_name="_call_tool")
    def _call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        调用MCP工具

        Args:
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            工具执行结果
        """
        if not self.mcp_client:
            if not self._ensure_server_running():
                raise Exception("无法连接到MCP服务器")

        try:
            result = self.mcp_client.call_tool(tool_name, arguments or {})
            if result and "error" not in result:
                return result
            else:
                error_msg = result.get("error", "未知错误") if result else "调用失败"
                raise Exception(f"工具调用失败: {error_msg}")

        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {e}")
            raise

    def test_connection(self) -> bool:
        """测试连接"""
        try:
            if not self.mcp_client:
                if not self._ensure_server_running():
                    return False

            # 尝试获取工具列表
            tools = self.mcp_client.list_tools()
            return tools is not None

        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    def get_api_version(self) -> str:
        """获取Zabbix API版本"""
        result = self._call_tool("apiinfo_version")
        return result.get("content", [{}])[0].get("text", "Unknown")

    # 以下是40个Zabbix API工具的封装方法

    def apiinfo_version(self) -> Dict[str, Any]:
        """获取API版本信息"""
        return self._call_tool("apiinfo_version")

    def hostgroup_get(self, **kwargs) -> Dict[str, Any]:
        """获取主机组"""
        return self._call_tool("hostgroup_get", kwargs)

    def host_get(self, **kwargs) -> Dict[str, Any]:
        """获取主机"""
        return self._call_tool("host_get", kwargs)

    def item_get(self, **kwargs) -> Dict[str, Any]:
        """获取监控项"""
        return self._call_tool("item_get", kwargs)

    def history_get(self, **kwargs) -> Dict[str, Any]:
        """获取历史数据"""
        return self._call_tool("history_get", kwargs)

    def trend_get(self, **kwargs) -> Dict[str, Any]:
        """获取趋势数据"""
        return self._call_tool("trend_get", kwargs)

    def problem_get(self, **kwargs) -> Dict[str, Any]:
        """获取问题"""
        return self._call_tool("problem_get", kwargs)

    def event_get(self, **kwargs) -> Dict[str, Any]:
        """获取事件"""
        return self._call_tool("event_get", kwargs)

    def trigger_get(self, **kwargs) -> Dict[str, Any]:
        """获取触发器"""
        return self._call_tool("trigger_get", kwargs)

    # 添加ZabbixAgent需要的辅助方法

    def get_hostgroup_id(self, hostgroup_name: str) -> Optional[str]:
        """根据主机组名称获取主机组ID"""
        try:
            result = self.hostgroup_get(
                filter={"name": hostgroup_name},
                output=["groupid"]
            )

            # 解析MCP响应
            if result and "content" in result:
                content = result["content"]
                if isinstance(content, list) and len(content) > 0:
                    # 假设content[0]包含text字段，其中有JSON数据
                    import json
                    data = json.loads(content[0].get("text", "[]"))
                    if data and len(data) > 0:
                        return data[0].get("groupid")

            return None

        except Exception as e:
            logger.error(f"获取主机组ID失败: {e}")
            return None

    def get_hosts_by_groupid(self, groupid: str) -> List[Dict[str, Any]]:
        """根据主机组ID获取主机列表"""
        try:
            result = self.host_get(
                groupids=[groupid],
                output=["hostid", "host", "name", "status"]
            )

            # 解析MCP响应
            if result and "content" in result:
                content = result["content"]
                if isinstance(content, list) and len(content) > 0:
                    import json
                    data = json.loads(content[0].get("text", "[]"))
                    return data if isinstance(data, list) else []

            return []

        except Exception as e:
            logger.error(f"获取主机列表失败: {e}")
            return []

    def get_items_by_hostids(self, hostids: List[str], keys: List[str] = None) -> List[Dict[str, Any]]:
        """根据主机ID列表获取监控项"""
        try:
            params = {
                "hostids": hostids,
                "output": ["itemid", "hostid", "key_", "name", "value_type"]
            }

            if keys:
                params["search"] = {"key_": keys}

            result = self.item_get(**params)

            # 解析MCP响应
            if result and "content" in result:
                content = result["content"]
                if isinstance(content, list) and len(content) > 0:
                    import json
                    data = json.loads(content[0].get("text", "[]"))
                    return data if isinstance(data, list) else []

            return []

        except Exception as e:
            logger.error(f"获取监控项失败: {e}")
            return []

    def get_history_data(self, itemids: List[str], time_from: int, time_till: int = None) -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            params = {
                "itemids": itemids,
                "time_from": time_from,
                "output": "extend",
                "sortfield": "clock",
                "sortorder": "DESC",
                "limit": 1000
            }

            if time_till:
                params["time_till"] = time_till

            result = self.history_get(**params)

            # 解析MCP响应
            if result and "content" in result:
                content = result["content"]
                if isinstance(content, list) and len(content) > 0:
                    import json
                    data = json.loads(content[0].get("text", "[]"))
                    return data if isinstance(data, list) else []

            return []

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []