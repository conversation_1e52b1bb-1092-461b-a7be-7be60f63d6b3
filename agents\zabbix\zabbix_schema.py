# -*- coding: utf-8 -*-

"""
File: zabbix_schema.py
Author: AI Assistant
Date: 2025/7/9

Zabbix Agent工具的参数Schema定义
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class GetComprehensiveMetricsSchema(BaseModel):
    """获取综合监控指标的参数Schema"""
    hostgroup_name: str = Field(..., description="主机组名称，如 'Linux servers' 或 'Web servers'")
    time_range_hours: Optional[int] = Field(24, description="时间范围（小时），默认24小时")
    include_metrics: Optional[List[str]] = Field(
        ["cpu", "memory", "disk", "network"],
        description="要包含的指标类型列表，可选: cpu, memory, disk, network, system"
    )
    limit_per_metric: Optional[int] = Field(100, description="每个指标的最大数据点数，默认100")


class GetProblemAnalysisDataSchema(BaseModel):
    """获取问题分析数据的参数Schema"""
    hostgroup_name: Optional[str] = Field(None, description="主机组名称，如果不指定则获取所有主机组的问题")
    severity_levels: Optional[List[int]] = Field(
        [3, 4, 5],
        description="问题严重级别列表，0=未分类, 1=信息, 2=警告, 3=一般严重, 4=严重, 5=灾难，默认获取3-5级"
    )
    time_range_hours: Optional[int] = Field(24, description="时间范围（小时），默认24小时")
    include_recent_only: Optional[bool] = Field(True, description="是否只包含最近的问题，默认true")
    limit: Optional[int] = Field(50, description="最大问题数量，默认50")


class GetPerformanceTrendsSchema(BaseModel):
    """获取性能趋势数据的参数Schema"""
    hostgroup_name: str = Field(..., description="主机组名称")
    metric_types: Optional[List[str]] = Field(
        ["cpu", "memory"],
        description="要分析趋势的指标类型，可选: cpu, memory, disk, network"
    )
    time_range_days: Optional[int] = Field(7, description="时间范围（天），默认7天")
    trend_analysis: Optional[bool] = Field(True, description="是否进行趋势分析，默认true")


class AnalyzeAndGenerateReportSchema(BaseModel):
    """分析数据并生成报告的参数Schema"""
    metrics_data_json: str = Field(..., description="监控指标数据的JSON字符串")
    problems_data_json: Optional[str] = Field(None, description="问题数据的JSON字符串（可选）")
    trends_data_json: Optional[str] = Field(None, description="趋势数据的JSON字符串（可选）")
    user_query: str = Field(..., description="用户的原始查询或分析需求")
    report_type: Optional[str] = Field(
        "comprehensive",
        description="报告类型: comprehensive(综合报告), performance(性能报告), problem(问题报告), capacity(容量规划)"
    )
    include_recommendations: Optional[bool] = Field(True, description="是否包含优化建议，默认true")


class GetHostGroupInfoSchema(BaseModel):
    """获取主机组信息的参数Schema"""
    hostgroup_name: Optional[str] = Field(None, description="主机组名称，如果不指定则获取所有主机组")
    include_hosts: Optional[bool] = Field(True, description="是否包含主机详细信息，默认true")
    include_items: Optional[bool] = Field(False, description="是否包含监控项信息，默认false")


class GetSystemHealthSchema(BaseModel):
    """获取系统健康状态的参数Schema"""
    hostgroup_name: Optional[str] = Field(None, description="主机组名称，如果不指定则检查所有主机组")
    check_problems: Optional[bool] = Field(True, description="是否检查当前问题，默认true")
    check_performance: Optional[bool] = Field(True, description="是否检查性能指标，默认true")
    performance_threshold: Optional[dict] = Field(
        {"cpu": 85, "memory": 90, "disk": 95},
        description="性能阈值设置，超过阈值将标记为异常"
    )