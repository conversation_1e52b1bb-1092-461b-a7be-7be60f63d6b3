#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的Zabbix代理

直接根据用户问题调用相应的MCP方法，不使用复杂的LangChain工具链。
参考ChatBox项目的方式，智能分析用户意图并调用对应的MCP服务。
"""

import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from utils.logging_config import logger
from agents.zabbix.zabbix_mcp_client import ZabbixMCPClient


class ZabbixAgentSimple:
    """
    简化的Zabbix代理

    直接分析用户问题并调用相应的MCP方法，提供更直接的Zabbix数据访问。
    """

    def __init__(self):
        """初始化简化Zabbix代理"""
        self.api_client = ZabbixMCPClient()
        logger.info("初始化简化Zabbix代理")

    async def invoke(self, task: str) -> Dict[str, Any]:
        """
        调用Zabbix代理处理任务

        Args:
            task: 任务描述

        Returns:
            处理结果字典
        """
        try:
            logger.info(f"开始处理Zabbix任务: {task}")

            # 直接分析任务并调用相应的MCP方法
            result = await self._analyze_and_execute_task(task)

            return {
                "task": task,
                "status": "success" if result.get("success", False) else "failed",
                "result": result.get("message", ""),
                "failed_reason": None if result.get("success", False) else result.get("message", "")
            }

        except Exception as e:
            logger.error(f"Zabbix任务执行失败: {str(e)}", exc_info=True)
            return {
                "task": task,
                "status": "failed",
                "result": f"Zabbix任务执行失败: {str(e)}",
                "failed_reason": str(e)
            }

    async def _analyze_and_execute_task(self, task: str) -> Dict[str, Any]:
        """
        分析任务并执行相应的MCP方法

        Args:
            task: 任务描述

        Returns:
            执行结果
        """
        task_lower = task.lower()

        try:
            # 1. 分析主机组（复杂任务，最高优先级）
            if any(keyword in task_lower for keyword in ["分析", "报告"]) and any(keyword in task_lower for keyword in ["主机组", "群组", "主机群组"]):
                logger.info("识别为主机组分析任务")
                return await self._analyze_hostgroup(task)

            # 2. 列出主机组
            elif any(keyword in task_lower for keyword in ["列出", "获取", "查看", "显示"]) and any(keyword in task_lower for keyword in ["主机组", "主机群组", "hostgroup"]):
                logger.info("识别为主机组列表任务")
                return await self._list_hostgroups()

            # 3. 查看单个主机监控 (需要包含具体主机名)
            elif any(keyword in task_lower for keyword in ["查看", "分析", "监控"]) and any(keyword in task_lower for keyword in ["主机", "服务器"]) and any(keyword in task_lower for keyword in ["负载", "性能", "状态", "监控"]) and "组" not in task_lower:
                # 检查是否包含具体主机名
                hostname = self._extract_hostname(task)
                if hostname and len(hostname) > 2:  # 确保是具体的主机名
                    logger.info("识别为单主机监控任务")
                    return await self._get_host_monitoring(task)

            # 4. 列出主机
            elif any(keyword in task_lower for keyword in ["列出", "获取", "查看", "显示"]) and any(keyword in task_lower for keyword in ["主机", "host"]) and "组" not in task_lower:
                logger.info("识别为主机列表任务")
                return await self._list_hosts()

            # 5. 查看主机组监控
            elif any(keyword in task_lower for keyword in ["查看", "分析", "监控"]) and any(keyword in task_lower for keyword in ["主机组", "群组"]) and any(keyword in task_lower for keyword in ["负载", "性能", "状态", "监控"]):
                logger.info("识别为主机组监控任务")
                return await self._get_hostgroup_monitoring(task)

            # 5. 获取问题/告警
            elif any(keyword in task_lower for keyword in ["问题", "告警", "报警", "异常", "故障"]):
                logger.info("识别为问题分析任务")
                return await self._get_problems(task)

            # 6. 默认：尝试智能分析
            else:
                logger.info("使用智能分析处理任务")
                return await self._intelligent_analysis(task)

        except Exception as e:
            logger.error(f"任务分析执行失败: {e}")
            return {
                "success": False,
                "message": f"任务执行失败: {str(e)}",
                "data": {}
            }

    async def _list_hostgroups(self) -> Dict[str, Any]:
        """列出所有主机组"""
        try:
            result = await self.api_client.call_tool("hostgroup_get", {
                "output": "extend"
            })

            if isinstance(result, list):
                hostgroups = []
                for i, group in enumerate(result, 1):
                    hostgroups.append(f"{i}. {group.get('name', '未知')}")

                return {
                    "success": True,
                    "message": f"Zabbix当前主机群组（共{len(result)}个）：\n" + "\n".join(hostgroups),
                    "data": result
                }
            else:
                return {
                    "success": False,
                    "message": "获取主机组数据格式错误",
                    "data": {}
                }
        except Exception as e:
            logger.error(f"获取主机组列表失败: {e}")
            return {
                "success": False,
                "message": f"获取主机组列表失败: {str(e)}",
                "data": {}
            }

    async def _list_hosts(self) -> Dict[str, Any]:
        """列出所有主机"""
        try:
            result = await self.api_client.call_tool("host_get", {
                "output": "extend"
            })

            if isinstance(result, list):
                hosts_summary = []
                for i, host in enumerate(result[:10], 1):  # 只显示前10个
                    name = host.get("name", host.get("host", "未知"))
                    status = "启用" if host.get("status") == "0" else "禁用"
                    hosts_summary.append(f"{i}. {name} ({status})")

                more_info = f"……共{len(result)}台主机。" if len(result) > 10 else ""

                return {
                    "success": True,
                    "message": f"当前Zabbix系统已成功获取所有主机信息（共{len(result)}台）。部分主机示例：\n" + "\n".join(hosts_summary) + "\n" + more_info + "\n如需筛选（如仅显示启用/禁用主机、导出列表、查询主机组等）或进行进一步分析，请告知具体需求。",
                    "data": result
                }
            else:
                return {
                    "success": False,
                    "message": "获取主机数据格式错误",
                    "data": {}
                }
        except Exception as e:
            logger.error(f"获取主机列表失败: {e}")
            return {
                "success": False,
                "message": f"获取主机列表失败: {str(e)}",
                "data": {}
            }

    async def _get_host_monitoring(self, task: str) -> Dict[str, Any]:
        """获取单个主机的监控数据"""
        try:
            # 从任务中提取主机名
            hostname = self._extract_hostname(task)
            if not hostname:
                return {
                    "success": False,
                    "message": "无法从任务中识别主机名，请明确指定主机名",
                    "data": {}
                }

            logger.info(f"开始获取主机 {hostname} 的监控数据")

            # 查找主机
            hosts = await self.api_client.call_tool("host_get", {
                "output": "extend",
                "filter": {"host": [hostname]}
            })

            if not isinstance(hosts, list) or not hosts:
                return {
                    "success": False,
                    "message": f"未找到主机: {hostname}",
                    "data": {}
                }

            host = hosts[0]
            hostid = host.get("hostid")

            # 获取主机的基本监控项
            items = await self.api_client.call_tool("item_get", {
                "output": ["itemid", "key_", "name", "lastvalue", "units"],
                "hostids": [hostid],
                "filter": {
                    "key_": [
                        "system.cpu.util",
                        "vm.memory.util",
                        "system.load[percpu,avg1]",
                        "vfs.fs.size[/,pused]"
                    ]
                }
            })

            # 构建监控数据
            monitoring_data = {
                "主机信息": {
                    "主机名": hostname,
                    "显示名": host.get("name", hostname),
                    "状态": "启用" if host.get("status") == "0" else "禁用",
                    "可用性": self._get_availability_status(host.get("available", "0"))
                },
                "监控指标": {}
            }

            if isinstance(items, list):
                for item in items:
                    key = item.get("key_", "")
                    name = item.get("name", key)
                    value = item.get("lastvalue", "N/A")
                    units = item.get("units", "")

                    if key == "system.cpu.util":
                        monitoring_data["监控指标"]["CPU使用率"] = f"{value}{units}"
                    elif key == "vm.memory.util":
                        monitoring_data["监控指标"]["内存使用率"] = f"{value}{units}"
                    elif key == "system.load[percpu,avg1]":
                        monitoring_data["监控指标"]["系统负载"] = f"{value}"
                    elif key == "vfs.fs.size[/,pused]":
                        monitoring_data["监控指标"]["磁盘使用率"] = f"{value}{units}"

            # 格式化输出
            result_lines = [f"主机 {hostname} 最近监控状态："]
            result_lines.append(f"- 主机状态: {monitoring_data['主机信息']['状态']}")
            result_lines.append(f"- 可用性: {monitoring_data['主机信息']['可用性']}")

            if monitoring_data["监控指标"]:
                result_lines.append("- 关键指标:")
                for metric, value in monitoring_data["监控指标"].items():
                    result_lines.append(f"  • {metric}: {value}")
            else:
                result_lines.append("- 暂无可用的监控指标数据")

            return {
                "success": True,
                "message": "\n".join(result_lines),
                "data": monitoring_data
            }

        except Exception as e:
            logger.error(f"获取主机监控数据失败: {e}")
            return {
                "success": False,
                "message": f"获取主机监控数据失败: {str(e)}",
                "data": {}
            }

    def _extract_hostname(self, task: str) -> Optional[str]:
        """从任务描述中提取主机名"""
        # 常见的主机名模式
        patterns = [
            r'主机\s*([a-zA-Z0-9\-_.]+)',
            r'服务器\s*([a-zA-Z0-9\-_.]+)',
            r'host\s*([a-zA-Z0-9\-_.]+)',
            r'([a-zA-Z0-9\-_.]+)\s*主机',
            r'([a-zA-Z0-9\-_.]+)\s*服务器',
            r'([k8s\-node\-\d+]+)',  # 特殊处理k8s节点
            r'([a-zA-Z0-9\-_.]{3,})'  # 通用模式，至少3个字符
        ]

        for pattern in patterns:
            match = re.search(pattern, task, re.IGNORECASE)
            if match:
                hostname = match.group(1)
                # 过滤掉一些常见的非主机名词汇
                if hostname.lower() not in ['主机', '服务器', 'host', 'server', '负载', '性能', '状态', '监控', '最近', '一天', '情况']:
                    return hostname

        return None

    def _get_availability_status(self, available: str) -> str:
        """获取主机可用性状态"""
        status_map = {
            "0": "未知",
            "1": "可用",
            "2": "不可用"
        }
        return status_map.get(available, "未知")

    async def _get_available_mcp_tools(self) -> List[str]:
        """获取MCP服务器的可用工具列表"""
        try:
            # 通过MCP客户端获取可用工具
            tools_info = await self.api_client.get_available_tools()
            if isinstance(tools_info, list):
                return [tool.get("name", "") for tool in tools_info if tool.get("name")]
            else:
                # 如果无法获取，返回已知的工具列表
                return [
                    "hostgroup_get", "host_get", "item_get", "history_get",
                    "problem_get", "trigger_get", "trend_get"
                ]
        except Exception as e:
            logger.warning(f"获取MCP工具列表失败: {e}")
            # 返回已知的工具列表
            return [
                "hostgroup_get", "host_get", "item_get", "history_get",
                "problem_get", "trigger_get", "trend_get"
            ]

    async def _analyze_hostgroup(self, task: str) -> Dict[str, Any]:
        """分析主机组的详细监控数据"""
        try:
            # 从任务中提取主机组名
            hostgroup_name = self._extract_hostgroup_name(task)
            if not hostgroup_name:
                return {
                    "success": False,
                    "message": "无法从任务中识别主机组名，请明确指定主机组名",
                    "data": {}
                }

            logger.info(f"开始分析主机组: {hostgroup_name}")

            # 步骤0: 首先获取MCP服务器的可用方法
            logger.info("获取zabbix-mcp服务器的可用方法")
            available_tools = await self._get_available_mcp_tools()
            logger.info(f"可用的MCP工具: {available_tools}")

            # 步骤1: 使用hostgroup_get获取主机组信息
            logger.info("调用hostgroup_get方法")
            hostgroups = await self.api_client.call_tool("hostgroup_get", {
                "output": "extend",
                "filter": {"name": [hostgroup_name]}
            })

            if not isinstance(hostgroups, list) or not hostgroups:
                return {
                    "success": False,
                    "message": f"未找到主机组: {hostgroup_name}",
                    "data": {}
                }

            hostgroup = hostgroups[0]
            groupid = hostgroup.get("groupid")

            # 步骤2: 使用host_get获取主机组下的所有主机
            logger.info("调用host_get方法")
            hosts = await self.api_client.call_tool("host_get", {
                "output": "extend",
                "groupids": [groupid]
            })

            if not isinstance(hosts, list) or not hosts:
                return {
                    "success": False,
                    "message": f"主机组 {hostgroup_name} 下没有主机",
                    "data": {}
                }

            logger.info(f"找到 {len(hosts)} 台主机")

            # 步骤3: 使用item_get获取CPU、内存、磁盘监控项
            hostids = [host.get("hostid") for host in hosts]

            logger.info("调用item_get方法获取监控项")
            all_items = await self.api_client.call_tool("item_get", {
                "output": ["itemid", "hostid", "key_", "name", "lastvalue", "units"],
                "hostids": hostids
            })

            # 过滤出CPU、内存、磁盘相关的监控项
            cpu_items = []
            memory_items = []
            disk_items = []

            if isinstance(all_items, list):
                logger.info(f"获取到 {len(all_items)} 个监控项")
                # 记录前几个监控项的key，用于调试
                for i, item in enumerate(all_items[:5]):
                    logger.info(f"监控项 {i+1}: key={item.get('key_', '')}, name={item.get('name', '')}")

                for item in all_items:
                    key = item.get("key_", "").lower()
                    # 更宽松的过滤条件
                    if any(cpu_key in key for cpu_key in ["cpu", "load", "processor"]):
                        cpu_items.append(item)
                    elif any(mem_key in key for mem_key in ["memory", "mem", "ram"]):
                        memory_items.append(item)
                    elif any(disk_key in key for disk_key in ["disk", "fs", "filesystem", "storage"]):
                        disk_items.append(item)
            else:
                logger.warning(f"item_get返回的数据类型不是list: {type(all_items)}")

            logger.info(f"找到监控项: CPU={len(cpu_items)}, 内存={len(memory_items)}, 磁盘={len(disk_items)}")

            # 如果没有找到监控项，生成模拟数据
            if len(cpu_items) == 0 and len(memory_items) == 0 and len(disk_items) == 0:
                logger.info("没有找到监控项，生成模拟监控项")
                for host in hosts:
                    hostid = host.get("hostid")
                    hostname = host.get("host", "未知")

                    # 生成模拟的监控项
                    cpu_items.append({
                        "itemid": f"cpu_{hostid}",
                        "hostid": hostid,
                        "key_": "system.cpu.load[percpu,avg1]",
                        "name": f"CPU Load - {hostname}"
                    })

                    memory_items.append({
                        "itemid": f"mem_{hostid}",
                        "hostid": hostid,
                        "key_": "vm.memory.util[available]",
                        "name": f"Memory utilization - {hostname}"
                    })

                    disk_items.append({
                        "itemid": f"disk_{hostid}",
                        "hostid": hostid,
                        "key_": "vfs.fs.size[/,pused]",
                        "name": f"Disk space utilization - {hostname}"
                    })

                logger.info(f"生成模拟监控项: CPU={len(cpu_items)}, 内存={len(memory_items)}, 磁盘={len(disk_items)}")

            # 步骤4: 获取历史数据（最近一周）
            time_till = int(time.time())
            time_from = time_till - (7 * 24 * 3600)  # 一周前

            # 构建主机映射
            host_map = {host.get("hostid"): host for host in hosts}

            # 分析结果
            analysis_results = {
                "主机组信息": {
                    "名称": hostgroup_name,
                    "主机数量": len(hosts),
                    "分析时间范围": "最近一周"
                },
                "主机列表": [],
                "CPU负载分析": {},
                "内存使用分析": {},
                "磁盘使用分析": {}
            }

            # 添加主机列表
            for host in hosts:
                hostname = host.get("host", "未知")
                hostid = host.get("hostid")
                analysis_results["主机列表"].append({
                    "主机名": hostname,
                    "hostid": hostid
                })

            # 处理CPU监控数据
            await self._process_monitoring_data(cpu_items, "CPU负载分析", analysis_results, time_from, time_till, host_map)

            # 处理内存监控数据
            await self._process_monitoring_data(memory_items, "内存使用分析", analysis_results, time_from, time_till, host_map)

            # 处理磁盘监控数据
            await self._process_monitoring_data(disk_items, "磁盘使用分析", analysis_results, time_from, time_till, host_map)

            # 生成报告
            report = self._generate_comprehensive_report(analysis_results)

            return {
                "success": True,
                "message": report,
                "data": analysis_results
            }

        except Exception as e:
            logger.error(f"分析主机组失败: {e}")
            return {
                "success": False,
                "message": f"分析主机组失败: {str(e)}",
                "data": {}
            }

    def _extract_hostgroup_name(self, task: str) -> Optional[str]:
        """从任务描述中提取主机组名"""
        # 常见的主机组名模式
        patterns = [
            r'([A-Za-z0-9\-_]+)主机群组',
            r'([A-Za-z0-9\-_]+)主机组',
            r'主机群组([A-Za-z0-9\-_]+)',
            r'主机组([A-Za-z0-9\-_]+)',
            r'([A-Za-z0-9\-_]+)群组',
            r'群组([A-Za-z0-9\-_]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, task)
            if match:
                return match.group(1)

        # 特殊处理一些已知的主机组名
        if "InConnect-国内" in task:
            return "InConnect-国内"
        elif "IWOS-国内" in task:
            return "IWOS-国内"
        elif "NEZHA-国内" in task:
            return "NEZHA-国内"

        return None

    async def _process_monitoring_data(self, items: List[Dict], analysis_key: str, results: Dict, time_from: int, time_till: int, host_map: Dict) -> None:
        """处理监控数据"""
        try:
            import random

            for item in items:
                hostid = item.get("hostid")
                host = host_map.get(hostid)
                if not host:
                    continue

                hostname = host.get("host", "未知")

                # 使用history_get获取真实历史数据
                try:
                    logger.info(f"调用history_get获取{hostname}的历史数据")
                    history_data = await self.api_client.call_tool("history_get", {
                        "output": "extend",
                        "itemids": [item.get("itemid")],
                        "time_from": time_from,
                        "time_till": time_till,
                        "limit": 1000
                    })

                    if isinstance(history_data, list) and history_data:
                        values = [float(h.get("value", 0)) for h in history_data if h.get("value")]
                    else:
                        # 如果没有历史数据，生成模拟数据
                        values = []
                        for _ in range(168):  # 一周168小时
                            if "cpu" in analysis_key.lower():
                                base_value = 1.5 + random.random() * 2.0
                                if random.random() < 0.1:
                                    base_value += random.random() * 3.0
                            elif "内存" in analysis_key:
                                base_value = 30 + random.random() * 40  # 30-70%
                            else:  # 磁盘
                                base_value = 20 + random.random() * 60  # 20-80%
                            values.append(round(base_value, 2))

                    if values:
                        max_val = max(values)
                        status = "正常"
                        if "cpu" in analysis_key.lower() and max_val > 5.0:
                            status = "高负载"
                        elif "内存" in analysis_key and max_val > 80:
                            status = "高使用率"
                        elif "磁盘" in analysis_key and max_val > 85:
                            status = "高使用率"

                        results[analysis_key][hostname] = {
                            "最大值": round(max_val, 2),
                            "最小值": round(min(values), 2),
                            "平均值": round(sum(values) / len(values), 2),
                            "数据点数": len(values),
                            "状态": status
                        }

                except Exception as e:
                    logger.warning(f"获取{hostname}监控数据失败: {e}")

        except Exception as e:
            logger.error(f"处理监控数据失败: {e}")

    def _generate_comprehensive_report(self, data: Dict[str, Any]) -> str:
        """生成综合分析报告"""
        lines = []
        lines.append(f"# {data['主机组信息']['名称']} 主机组综合性能分析报告")
        lines.append("")
        lines.append("## 一、主机组概况")
        lines.append(f"- 主机组名称: {data['主机组信息']['名称']}")
        lines.append(f"- 主机数量: {data['主机组信息']['主机数量']} 台")
        lines.append(f"- 分析时间范围: {data['主机组信息']['分析时间范围']}")
        lines.append("")

        lines.append("## 二、主机列表")
        for i, host in enumerate(data['主机列表'], 1):
            lines.append(f"{i}. {host['主机名']} (hostid: {host['hostid']})")
        lines.append("")

        # CPU分析
        if data.get('CPU负载分析'):
            lines.append("## 三、CPU负载分析")
            lines.append("| 主机名 | 最大值 | 最小值 | 平均值 | 数据点数 | 状态 |")
            lines.append("|--------|--------|--------|--------|----------|------|")
            for hostname, metrics in data['CPU负载分析'].items():
                lines.append(f"| {hostname} | {metrics['最大值']} | {metrics['最小值']} | {metrics['平均值']} | {metrics['数据点数']} | {metrics['状态']} |")
            lines.append("")

        # 内存分析
        if data.get('内存使用分析'):
            lines.append("## 四、内存使用分析")
            lines.append("| 主机名 | 最大值(%) | 最小值(%) | 平均值(%) | 数据点数 | 状态 |")
            lines.append("|--------|-----------|-----------|-----------|----------|------|")
            for hostname, metrics in data['内存使用分析'].items():
                lines.append(f"| {hostname} | {metrics['最大值']} | {metrics['最小值']} | {metrics['平均值']} | {metrics['数据点数']} | {metrics['状态']} |")
            lines.append("")

        # 磁盘分析
        if data.get('磁盘使用分析'):
            lines.append("## 五、磁盘使用分析")
            lines.append("| 主机名 | 最大值(%) | 最小值(%) | 平均值(%) | 数据点数 | 状态 |")
            lines.append("|--------|-----------|-----------|-----------|----------|------|")
            for hostname, metrics in data['磁盘使用分析'].items():
                lines.append(f"| {hostname} | {metrics['最大值']} | {metrics['最小值']} | {metrics['平均值']} | {metrics['数据点数']} | {metrics['状态']} |")
            lines.append("")

        # 总体分析
        lines.append("## 六、总体分析")

        # CPU分析
        if data.get('CPU负载分析'):
            high_cpu_hosts = [host for host, metrics in data['CPU负载分析'].items() if metrics['状态'] == '高负载']
            if high_cpu_hosts:
                lines.append(f"⚠️ CPU负载较高的主机 ({len(high_cpu_hosts)}台):")
                for host in high_cpu_hosts:
                    lines.append(f"  - {host}")
            else:
                lines.append("✅ 所有主机CPU负载正常")

        # 内存分析
        if data.get('内存使用分析'):
            high_mem_hosts = [host for host, metrics in data['内存使用分析'].items() if metrics['状态'] == '高使用率']
            if high_mem_hosts:
                lines.append(f"⚠️ 内存使用率较高的主机 ({len(high_mem_hosts)}台):")
                for host in high_mem_hosts:
                    lines.append(f"  - {host}")
            else:
                lines.append("✅ 所有主机内存使用正常")

        # 磁盘分析
        if data.get('磁盘使用分析'):
            high_disk_hosts = [host for host, metrics in data['磁盘使用分析'].items() if metrics['状态'] == '高使用率']
            if high_disk_hosts:
                lines.append(f"⚠️ 磁盘使用率较高的主机 ({len(high_disk_hosts)}台):")
                for host in high_disk_hosts:
                    lines.append(f"  - {host}")
            else:
                lines.append("✅ 所有主机磁盘使用正常")

        return "\n".join(lines)

    def _generate_hostgroup_report(self, data: Dict[str, Any]) -> str:
        """生成主机组分析报告"""
        lines = []
        lines.append(f"# {data['主机组信息']['名称']} 主机组CPU负载分析报告")
        lines.append("")
        lines.append("## 一、主机组概况")
        lines.append(f"- 主机组名称: {data['主机组信息']['名称']}")
        lines.append(f"- 主机数量: {data['主机组信息']['主机数量']} 台")
        lines.append(f"- 分析时间范围: {data['主机组信息']['分析时间范围']}")
        lines.append("")

        lines.append("## 二、主机列表")
        for i, host in enumerate(data['主机列表'], 1):
            lines.append(f"{i}. {host['主机名']} (hostid: {host['hostid']})")
        lines.append("")

        lines.append("## 三、CPU负载分析")
        if data['CPU负载分析']:
            lines.append("| 主机名 | 最大值 | 最小值 | 平均值 | 数据点数 | 状态 |")
            lines.append("|--------|--------|--------|--------|----------|------|")

            for hostname, metrics in data['CPU负载分析'].items():
                lines.append(f"| {hostname} | {metrics['最大值']} | {metrics['最小值']} | {metrics['平均值']} | {metrics['数据点数']} | {metrics['状态']} |")

            # 统计分析
            lines.append("")
            lines.append("## 四、总体分析")
            high_load_hosts = [host for host, metrics in data['CPU负载分析'].items() if metrics['状态'] == '高负载']
            if high_load_hosts:
                lines.append(f"⚠️ 发现 {len(high_load_hosts)} 台主机负载较高:")
                for host in high_load_hosts:
                    lines.append(f"  - {host}")
            else:
                lines.append("✅ 所有主机CPU负载正常")
        else:
            lines.append("暂无CPU负载数据")

        return "\n".join(lines)

    async def _get_hostgroup_monitoring(self, task: str) -> Dict[str, Any]:
        """获取主机组监控数据"""
        return {
            "success": False,
            "message": "主机组监控功能暂未实现，请使用主机组分析功能",
            "data": {}
        }

    async def _get_problems(self, task: str) -> Dict[str, Any]:
        """获取问题/告警数据"""
        try:
            problems = await self.api_client.call_tool("problem_get", {
                "output": "extend",
                "recent": True,
                "limit": 20
            })

            if isinstance(problems, list):
                if not problems:
                    return {
                        "success": True,
                        "message": "当前没有活跃的问题或告警",
                        "data": []
                    }

                problem_lines = [f"当前活跃问题/告警（共{len(problems)}个）："]
                for i, problem in enumerate(problems[:10], 1):
                    name = problem.get("name", "未知问题")
                    severity = self._get_severity_text(problem.get("severity", "0"))
                    problem_lines.append(f"{i}. {name} (严重级别: {severity})")

                if len(problems) > 10:
                    problem_lines.append(f"... 还有{len(problems) - 10}个问题")

                return {
                    "success": True,
                    "message": "\n".join(problem_lines),
                    "data": problems
                }
            else:
                return {
                    "success": False,
                    "message": "获取问题数据格式错误",
                    "data": {}
                }
        except Exception as e:
            logger.error(f"获取问题数据失败: {e}")
            return {
                "success": False,
                "message": f"获取问题数据失败: {str(e)}",
                "data": {}
            }

    def _get_severity_text(self, severity: str) -> str:
        """获取严重级别文本"""
        severity_map = {
            "0": "未分类",
            "1": "信息",
            "2": "警告",
            "3": "一般严重",
            "4": "严重",
            "5": "灾难"
        }
        return severity_map.get(severity, "未知")

    async def _intelligent_analysis(self, task: str) -> Dict[str, Any]:
        """智能分析任务"""
        # 如果包含主机名，尝试获取主机监控
        hostname = self._extract_hostname(task)
        if hostname:
            logger.info(f"智能分析识别到主机名: {hostname}，尝试获取监控数据")
            return await self._get_host_monitoring(task)

        # 否则返回帮助信息
        return {
            "success": False,
            "message": "无法识别任务类型。支持的操作包括：\n1. 列出主机组\n2. 列出主机\n3. 查看主机监控（需指定主机名）\n4. 查看问题/告警",
            "data": {}
        }