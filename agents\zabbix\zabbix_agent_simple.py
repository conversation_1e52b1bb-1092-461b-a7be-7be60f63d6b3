#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的Zabbix代理

直接根据用户问题调用相应的MCP方法，不使用复杂的LangChain工具链。
参考ChatBox项目的方式，智能分析用户意图并调用对应的MCP服务。
"""

import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from utils.logging_config import logger
from agents.zabbix.zabbix_mcp_client import ZabbixMCPClient


class ZabbixAgentSimple:
    """
    简化的Zabbix代理

    直接分析用户问题并调用相应的MCP方法，提供更直接的Zabbix数据访问。
    """

    def __init__(self):
        """初始化简化Zabbix代理"""
        self.api_client = ZabbixMCPClient()
        logger.info("初始化简化Zabbix代理")

    async def invoke(self, task: str) -> Dict[str, Any]:
        """
        调用Zabbix代理处理任务

        Args:
            task: 任务描述

        Returns:
            处理结果字典
        """
        try:
            logger.info(f"开始处理Zabbix任务: {task}")

            # 直接分析任务并调用相应的MCP方法
            result = await self._analyze_and_execute_task(task)

            return {
                "task": task,
                "status": "success" if result.get("success", False) else "failed",
                "result": result.get("message", ""),
                "failed_reason": None if result.get("success", False) else result.get("message", "")
            }

        except Exception as e:
            logger.error(f"Zabbix任务执行失败: {str(e)}", exc_info=True)
            return {
                "task": task,
                "status": "failed",
                "result": f"Zabbix任务执行失败: {str(e)}",
                "failed_reason": str(e)
            }

    async def _analyze_and_execute_task(self, task: str) -> Dict[str, Any]:
        """
        分析任务并执行相应的MCP方法

        Args:
            task: 任务描述

        Returns:
            执行结果
        """
        task_lower = task.lower()

        try:
            # 1. 列出主机组
            if any(keyword in task_lower for keyword in ["列出", "获取", "查看", "显示"]) and any(keyword in task_lower for keyword in ["主机组", "主机群组", "hostgroup"]):
                logger.info("识别为主机组列表任务")
                return await self._list_hostgroups()

            # 2. 查看单个主机监控 (优先级高于列出主机)
            elif any(keyword in task_lower for keyword in ["查看", "分析", "监控"]) and any(keyword in task_lower for keyword in ["主机", "服务器"]) and any(keyword in task_lower for keyword in ["负载", "性能", "状态", "监控"]):
                logger.info("识别为单主机监控任务")
                return await self._get_host_monitoring(task)

            # 3. 列出主机
            elif any(keyword in task_lower for keyword in ["列出", "获取", "查看", "显示"]) and any(keyword in task_lower for keyword in ["主机", "host"]) and "组" not in task_lower:
                logger.info("识别为主机列表任务")
                return await self._list_hosts()

            # 4. 查看主机组监控
            elif any(keyword in task_lower for keyword in ["查看", "分析", "监控"]) and any(keyword in task_lower for keyword in ["主机组", "群组"]) and any(keyword in task_lower for keyword in ["负载", "性能", "状态", "监控"]):
                logger.info("识别为主机组监控任务")
                return await self._get_hostgroup_monitoring(task)

            # 5. 获取问题/告警
            elif any(keyword in task_lower for keyword in ["问题", "告警", "报警", "异常", "故障"]):
                logger.info("识别为问题分析任务")
                return await self._get_problems(task)

            # 6. 默认：尝试智能分析
            else:
                logger.info("使用智能分析处理任务")
                return await self._intelligent_analysis(task)

        except Exception as e:
            logger.error(f"任务分析执行失败: {e}")
            return {
                "success": False,
                "message": f"任务执行失败: {str(e)}",
                "data": {}
            }

    async def _list_hostgroups(self) -> Dict[str, Any]:
        """列出所有主机组"""
        try:
            result = await self.api_client.call_tool("hostgroup_get", {
                "output": "extend"
            })

            if isinstance(result, list):
                hostgroups = []
                for i, group in enumerate(result, 1):
                    hostgroups.append(f"{i}. {group.get('name', '未知')}")

                return {
                    "success": True,
                    "message": f"Zabbix当前主机群组（共{len(result)}个）：\n" + "\n".join(hostgroups),
                    "data": result
                }
            else:
                return {
                    "success": False,
                    "message": "获取主机组数据格式错误",
                    "data": {}
                }
        except Exception as e:
            logger.error(f"获取主机组列表失败: {e}")
            return {
                "success": False,
                "message": f"获取主机组列表失败: {str(e)}",
                "data": {}
            }

    async def _list_hosts(self) -> Dict[str, Any]:
        """列出所有主机"""
        try:
            result = await self.api_client.call_tool("host_get", {
                "output": "extend"
            })

            if isinstance(result, list):
                hosts_summary = []
                for i, host in enumerate(result[:10], 1):  # 只显示前10个
                    name = host.get("name", host.get("host", "未知"))
                    status = "启用" if host.get("status") == "0" else "禁用"
                    hosts_summary.append(f"{i}. {name} ({status})")

                more_info = f"……共{len(result)}台主机。" if len(result) > 10 else ""

                return {
                    "success": True,
                    "message": f"当前Zabbix系统已成功获取所有主机信息（共{len(result)}台）。部分主机示例：\n" + "\n".join(hosts_summary) + "\n" + more_info + "\n如需筛选（如仅显示启用/禁用主机、导出列表、查询主机组等）或进行进一步分析，请告知具体需求。",
                    "data": result
                }
            else:
                return {
                    "success": False,
                    "message": "获取主机数据格式错误",
                    "data": {}
                }
        except Exception as e:
            logger.error(f"获取主机列表失败: {e}")
            return {
                "success": False,
                "message": f"获取主机列表失败: {str(e)}",
                "data": {}
            }

    async def _get_host_monitoring(self, task: str) -> Dict[str, Any]:
        """获取单个主机的监控数据"""
        try:
            # 从任务中提取主机名
            hostname = self._extract_hostname(task)
            if not hostname:
                return {
                    "success": False,
                    "message": "无法从任务中识别主机名，请明确指定主机名",
                    "data": {}
                }

            logger.info(f"开始获取主机 {hostname} 的监控数据")

            # 查找主机
            hosts = await self.api_client.call_tool("host_get", {
                "output": "extend",
                "filter": {"host": [hostname]}
            })

            if not isinstance(hosts, list) or not hosts:
                return {
                    "success": False,
                    "message": f"未找到主机: {hostname}",
                    "data": {}
                }

            host = hosts[0]
            hostid = host.get("hostid")

            # 获取主机的基本监控项
            items = await self.api_client.call_tool("item_get", {
                "output": ["itemid", "key_", "name", "lastvalue", "units"],
                "hostids": [hostid],
                "filter": {
                    "key_": [
                        "system.cpu.util",
                        "vm.memory.util",
                        "system.load[percpu,avg1]",
                        "vfs.fs.size[/,pused]"
                    ]
                }
            })

            # 构建监控数据
            monitoring_data = {
                "主机信息": {
                    "主机名": hostname,
                    "显示名": host.get("name", hostname),
                    "状态": "启用" if host.get("status") == "0" else "禁用",
                    "可用性": self._get_availability_status(host.get("available", "0"))
                },
                "监控指标": {}
            }

            if isinstance(items, list):
                for item in items:
                    key = item.get("key_", "")
                    name = item.get("name", key)
                    value = item.get("lastvalue", "N/A")
                    units = item.get("units", "")

                    if key == "system.cpu.util":
                        monitoring_data["监控指标"]["CPU使用率"] = f"{value}{units}"
                    elif key == "vm.memory.util":
                        monitoring_data["监控指标"]["内存使用率"] = f"{value}{units}"
                    elif key == "system.load[percpu,avg1]":
                        monitoring_data["监控指标"]["系统负载"] = f"{value}"
                    elif key == "vfs.fs.size[/,pused]":
                        monitoring_data["监控指标"]["磁盘使用率"] = f"{value}{units}"

            # 格式化输出
            result_lines = [f"主机 {hostname} 最近监控状态："]
            result_lines.append(f"- 主机状态: {monitoring_data['主机信息']['状态']}")
            result_lines.append(f"- 可用性: {monitoring_data['主机信息']['可用性']}")

            if monitoring_data["监控指标"]:
                result_lines.append("- 关键指标:")
                for metric, value in monitoring_data["监控指标"].items():
                    result_lines.append(f"  • {metric}: {value}")
            else:
                result_lines.append("- 暂无可用的监控指标数据")

            return {
                "success": True,
                "message": "\n".join(result_lines),
                "data": monitoring_data
            }

        except Exception as e:
            logger.error(f"获取主机监控数据失败: {e}")
            return {
                "success": False,
                "message": f"获取主机监控数据失败: {str(e)}",
                "data": {}
            }

    def _extract_hostname(self, task: str) -> Optional[str]:
        """从任务描述中提取主机名"""
        # 常见的主机名模式
        patterns = [
            r'主机\s*([a-zA-Z0-9\-_.]+)',
            r'服务器\s*([a-zA-Z0-9\-_.]+)',
            r'host\s*([a-zA-Z0-9\-_.]+)',
            r'([a-zA-Z0-9\-_.]+)\s*主机',
            r'([a-zA-Z0-9\-_.]+)\s*服务器',
            r'([k8s\-node\-\d+]+)',  # 特殊处理k8s节点
            r'([a-zA-Z0-9\-_.]{3,})'  # 通用模式，至少3个字符
        ]

        for pattern in patterns:
            match = re.search(pattern, task, re.IGNORECASE)
            if match:
                hostname = match.group(1)
                # 过滤掉一些常见的非主机名词汇
                if hostname.lower() not in ['主机', '服务器', 'host', 'server', '负载', '性能', '状态', '监控', '最近', '一天', '情况']:
                    return hostname

        return None

    def _get_availability_status(self, available: str) -> str:
        """获取主机可用性状态"""
        status_map = {
            "0": "未知",
            "1": "可用",
            "2": "不可用"
        }
        return status_map.get(available, "未知")

    async def _get_hostgroup_monitoring(self, task: str) -> Dict[str, Any]:
        """获取主机组监控数据"""
        return {
            "success": False,
            "message": "主机组监控功能暂未实现，请使用单主机监控功能",
            "data": {}
        }

    async def _get_problems(self, task: str) -> Dict[str, Any]:
        """获取问题/告警数据"""
        try:
            problems = await self.api_client.call_tool("problem_get", {
                "output": "extend",
                "recent": True,
                "limit": 20
            })

            if isinstance(problems, list):
                if not problems:
                    return {
                        "success": True,
                        "message": "当前没有活跃的问题或告警",
                        "data": []
                    }

                problem_lines = [f"当前活跃问题/告警（共{len(problems)}个）："]
                for i, problem in enumerate(problems[:10], 1):
                    name = problem.get("name", "未知问题")
                    severity = self._get_severity_text(problem.get("severity", "0"))
                    problem_lines.append(f"{i}. {name} (严重级别: {severity})")

                if len(problems) > 10:
                    problem_lines.append(f"... 还有{len(problems) - 10}个问题")

                return {
                    "success": True,
                    "message": "\n".join(problem_lines),
                    "data": problems
                }
            else:
                return {
                    "success": False,
                    "message": "获取问题数据格式错误",
                    "data": {}
                }
        except Exception as e:
            logger.error(f"获取问题数据失败: {e}")
            return {
                "success": False,
                "message": f"获取问题数据失败: {str(e)}",
                "data": {}
            }

    def _get_severity_text(self, severity: str) -> str:
        """获取严重级别文本"""
        severity_map = {
            "0": "未分类",
            "1": "信息",
            "2": "警告",
            "3": "一般严重",
            "4": "严重",
            "5": "灾难"
        }
        return severity_map.get(severity, "未知")

    async def _intelligent_analysis(self, task: str) -> Dict[str, Any]:
        """智能分析任务"""
        # 如果包含主机名，尝试获取主机监控
        hostname = self._extract_hostname(task)
        if hostname:
            logger.info(f"智能分析识别到主机名: {hostname}，尝试获取监控数据")
            return await self._get_host_monitoring(task)

        # 否则返回帮助信息
        return {
            "success": False,
            "message": "无法识别任务类型。支持的操作包括：\n1. 列出主机组\n2. 列出主机\n3. 查看主机监控（需指定主机名）\n4. 查看问题/告警",
            "data": {}
        }