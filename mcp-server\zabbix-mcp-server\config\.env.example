# Zabbix MCP Server Configuration

# Required: Zabbix server URL
ZABBIX_URL=https://zabbix.example.com

# Authentication Method 1: API Token (recommended)
ZABBIX_TOKEN=<your_api_token>

# Authentication Method 2: Username/Password (alternative to token)
# ZABBIX_USER=<username>
# ZABBIX_PASSWORD=<password>

# Optional: Read-only mode (only GET operations allowed)
# Set to true, 1, or yes to enable read-only mode
READ_ONLY=true