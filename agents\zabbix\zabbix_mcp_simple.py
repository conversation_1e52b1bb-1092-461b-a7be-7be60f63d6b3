#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix MCP简化客户端

使用官方MCP Python SDK连接zabbix-mcp-server，提供可靠的MCP通信。
"""

import os
import json
import asyncio
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional, List
from utils.logging_config import logger, log_tool_execution

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
except ImportError:
    logger.error("MCP SDK未安装，请运行: uv pip install mcp")
    raise


class ZabbixMCPSimple:
    """
    Zabbix MCP简化客户端

    使用官方MCP Python SDK连接zabbix-mcp-server。
    """

    def __init__(self, auto_start_server: bool = True):
        """
        初始化Zabbix MCP客户端

        Args:
            auto_start_server: 是否自动启动MCP服务器
        """
        self.auto_start_server = auto_start_server
        self.session = None
        self.read_stream = None
        self.write_stream = None
        self.stdio_context = None

        # MCP服务器配置
        self.mcp_server_dir = Path(__file__).parent.parent.parent / "mcp-server" / "zabbix-mcp-server"

        logger.info("初始化Zabbix MCP简化客户端")

    async def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            # 配置服务器参数
            server_params = StdioServerParameters(
                command="uv",
                args=["run", "python", "src/zabbix_mcp_server.py"],
                cwd=str(self.mcp_server_dir)
            )

            # 创建STDIO客户端连接
            self.stdio_context = stdio_client(server_params)
            self.read_stream, self.write_stream = await self.stdio_context.__aenter__()

            # 创建客户端会话
            self.session = ClientSession(self.read_stream, self.write_stream)

            # 初始化会话
            await self.session.initialize()

            logger.info("MCP客户端连接成功")
            return True

        except Exception as e:
            logger.error(f"MCP客户端连接失败: {e}")
            return False

    async def disconnect(self):
        """断开连接"""
        try:
            if self.session:
                await self.session.close()
                self.session = None

            if self.stdio_context:
                await self.stdio_context.__aexit__(None, None, None)
                self.stdio_context = None

        except Exception as e:
            logger.error(f"断开连接失败: {e}")

    async def _ensure_connected(self):
        """确保连接有效"""
        if not self.session:
            if not await self.connect():
                raise Exception("无法连接到MCP服务器")

    @log_tool_execution(tool_name="call_tool")
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        调用MCP工具

        Args:
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            工具执行结果
        """
        await self._ensure_connected()

        if arguments is None:
            arguments = {}

        try:
            result = await self.session.call_tool(tool_name, arguments)

            # 处理结果
            if result.isError:
                raise Exception(f"工具调用错误: {result.content}")

            # 解析内容
            content_data = []
            for item in result.content:
                if hasattr(item, 'text'):
                    content_data.append(item.text)
                elif hasattr(item, 'data'):
                    content_data.append(item.data)

            # 尝试解析JSON
            if content_data:
                try:
                    return json.loads(content_data[0])
                except json.JSONDecodeError:
                    return {"result": content_data[0]}

            return {"result": "No content"}

        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {e}")
            raise

    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            await self._ensure_connected()

            # 尝试获取API版本
            result = await self.call_tool("apiinfo_version")
            return True

        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    # 以下是ZabbixAgent需要的方法

    async def get_hostgroup_id(self, hostgroup_name: str) -> Optional[str]:
        """根据主机组名称获取主机组ID"""
        try:
            result = await self.call_tool("hostgroup_get", {
                "filter": {"name": hostgroup_name},
                "output": ["groupid"]
            })

            if isinstance(result, list) and len(result) > 0:
                return result[0].get("groupid")

            return None

        except Exception as e:
            logger.error(f"获取主机组ID失败: {e}")
            return None

    async def get_host_ids_in_group(self, groupid: str) -> List[str]:
        """根据主机组ID获取主机ID列表"""
        try:
            result = await self.call_tool("host_get", {
                "groupids": [groupid],
                "output": ["hostid"]
            })

            if isinstance(result, list):
                return [host["hostid"] for host in result]

            return []

        except Exception as e:
            logger.error(f"获取主机ID列表失败: {e}")
            return []

    async def get_item_ids(self, hostids: List[str], keys: List[str]) -> Dict[str, List[str]]:
        """根据主机ID和监控项key获取监控项ID字典"""
        try:
            result_dict = {}
            for key in keys:
                result = await self.call_tool("item_get", {
                    "hostids": hostids,
                    "search": {"key_": key},
                    "output": ["itemid"]
                })

                if isinstance(result, list) and result:
                    result_dict[key] = [item["itemid"] for item in result]

            return result_dict

        except Exception as e:
            logger.error(f"获取监控项ID失败: {e}")
            return {}

    async def get_history(self, itemids: List[str], time_from: int, time_till: int = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            params = {
                "itemids": itemids,
                "time_from": time_from,
                "output": "extend",
                "sortfield": "clock",
                "sortorder": "DESC",
                "limit": limit
            }

            if time_till:
                params["time_till"] = time_till

            result = await self.call_tool("history_get", params)
            return result if isinstance(result, list) else []

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []

    async def get_trends(self, itemids: List[str], time_from: int, time_till: int = None) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        try:
            params = {
                "itemids": itemids,
                "time_from": time_from,
                "output": "extend"
            }

            if time_till:
                params["time_till"] = time_till

            result = await self.call_tool("trend_get", params)
            return result if isinstance(result, list) else []

        except Exception as e:
            logger.error(f"获取趋势数据失败: {e}")
            return []

    async def get_problems(self, groupids: List[str] = None, severities: List[int] = None,
                          time_from: int = None, time_till: int = None, recent: bool = True,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """获取问题数据"""
        try:
            params = {
                "output": "extend",
                "sortfield": "eventid",
                "sortorder": "DESC",
                "limit": limit
            }

            if groupids:
                params["groupids"] = groupids
            if severities:
                params["severities"] = severities
            if time_from:
                params["time_from"] = time_from
            if time_till:
                params["time_till"] = time_till
            if recent:
                params["recent"] = "true"

            result = await self.call_tool("problem_get", params)
            return result if isinstance(result, list) else []

        except Exception as e:
            logger.error(f"获取问题数据失败: {e}")
            return []

    async def get_triggers(self, groupids: List[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取触发器数据"""
        try:
            params = {
                "output": "extend",
                "limit": limit
            }

            if groupids:
                params["groupids"] = groupids

            result = await self.call_tool("trigger_get", params)
            return result if isinstance(result, list) else []

        except Exception as e:
            logger.error(f"获取触发器数据失败: {e}")
            return []