[project]
name = "zabbix-mcp-server"
version = "1.0.0"
description = "A comprehensive MCP server for Zabbix integration"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Zabbix MCP Server Contributors"}
]
keywords = ["zabbix", "mcp", "monitoring", "api", "server"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: System Administrators",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: System :: Monitoring",
    "Topic :: System :: Systems Administration",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
"fastmcp>=2.8.1",
"zabbix_utils>=2.0.3",
"python-dotenv>=1.1.0"
]
requires-python = ">=3.10"

[project.urls]
Homepage = "https://github.com/mpeirone/zabbix-mcp-server"
Repository = "https://github.com/mpeirone/zabbix-mcp-server"
Issues = "https://github.com/mpeirone/zabbix-mcp-server/issues"
Documentation = "https://github.com/mpeirone/zabbix-mcp-server#readme"

[project.scripts]
zabbix-mcp = "src.zabbix_mcp_server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]