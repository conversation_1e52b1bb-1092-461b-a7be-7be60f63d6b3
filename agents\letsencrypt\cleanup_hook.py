#!/usr/bin/env python
import os
import sys
import subprocess

# 获取certbot环境变量
domain = os.environ.get('CERTBOT_DOMAIN', '')
validation = os.environ.get('CERTBOT_VALIDATION', '')

# 调用阿里云DNS脚本
cmd = [
    r"C:\Users\<USER>\Desktop\映瀚通\ai-ops-agent\ops-agent\.venv\Scripts\python.exe",
    r"C:\Users\<USER>\Desktop\映瀚通\ai-ops-agent\ops-agent\agents\letsencrypt\aliyun_dns_script.py",
    "clean",
    domain,
    "_acme-challenge",
    validation,
    "LTAI4G2iPYQW3QsLK4MdLHsE",
    "******************************"
]

result = subprocess.run(cmd, capture_output=True, text=True)
print(result.stdout)
if result.stderr:
    print(result.stderr, file=sys.stderr)
sys.exit(result.returncode)
