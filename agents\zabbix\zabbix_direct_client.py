#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix直接API客户端

直接使用zabbix_utils连接Zabbix，跳过MCP服务器，提供更可靠的连接。
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
from utils.logging_config import logger, log_tool_execution

try:
    from zabbix_utils import ZabbixAPI
except ImportError:
    logger.error("zabbix_utils未安装，请运行: pip install zabbix_utils")
    raise


class ZabbixDirectClient:
    """
    Zabbix直接API客户端

    直接使用zabbix_utils连接Zabbix，提供40个Zabbix API工具的访问接口。
    """

    def __init__(self):
        """初始化Zabbix直接客户端"""
        self.api: Optional[ZabbixAPI] = None
        self.timeout = int(os.getenv("ZABBIX_API_TIMEOUT", "30"))
        self.max_retries = int(os.getenv("ZABBIX_API_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("ZABBIX_API_RETRY_DELAY", "1.0"))

        # 加载环境变量
        env_file = Path(__file__).parent.parent.parent / "mcp-server" / "zabbix-mcp-server" / "config" / ".env"
        if env_file.exists():
            load_dotenv(env_file)

        logger.info("初始化Zabbix直接客户端")
        self._connect()

    def _connect(self) -> bool:
        """连接到Zabbix服务器"""
        try:
            url = os.getenv("ZABBIX_URL")
            token = os.getenv("ZABBIX_TOKEN")
            user = os.getenv("ZABBIX_USER")
            password = os.getenv("ZABBIX_PASSWORD")

            if not url:
                raise ValueError("未配置ZABBIX_URL")

            logger.info(f"连接到Zabbix服务器: {url}")

            self.api = ZabbixAPI(url=url)

            # 优先使用Token认证
            if token:
                logger.info("使用API Token认证")
                self.api.login(token=token)
            elif user and password:
                logger.info(f"使用用户名密码认证: {user}")
                self.api.login(user=user, password=password)
            else:
                raise ValueError("未配置认证信息（ZABBIX_TOKEN 或 ZABBIX_USER/ZABBIX_PASSWORD）")

            # 测试连接
            version = self.api.apiinfo.version()
            logger.info(f"Zabbix连接成功，版本: {version}")

            return True

        except Exception as e:
            logger.error(f"连接Zabbix失败: {e}")
            self.api = None
            return False

    def _ensure_connected(self):
        """确保连接有效"""
        if not self.api:
            if not self._connect():
                raise Exception("无法连接到Zabbix服务器")

    @log_tool_execution(tool_name="_call_api")
    def _call_api(self, method: str, params: Dict[str, Any] = None) -> Any:
        """
        调用Zabbix API

        Args:
            method: API方法名（如 'hostgroup.get'）
            params: API参数

        Returns:
            API响应结果
        """
        self._ensure_connected()

        if params is None:
            params = {}

        try:
            # 解析方法名
            if '.' in method:
                object_name, method_name = method.split('.', 1)
                api_object = getattr(self.api, object_name)
                api_method = getattr(api_object, method_name)
            else:
                api_method = getattr(self.api, method)

            # 调用API
            result = api_method(**params)
            return result

        except Exception as e:
            logger.error(f"调用API {method} 失败: {e}")
            raise

    def test_connection(self) -> bool:
        """测试连接"""
        try:
            self._ensure_connected()
            version = self.api.apiinfo.version()
            return True
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    def get_api_version(self) -> str:
        """获取Zabbix API版本"""
        try:
            self._ensure_connected()
            return self.api.apiinfo.version()
        except Exception as e:
            logger.error(f"获取API版本失败: {e}")
            return "Unknown"