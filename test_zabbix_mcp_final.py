#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Zabbix MCP客户端最终版本
"""

import sys
import asyncio
sys.path.append('agents/zabbix')

async def test_zabbix_mcp_simple():
    """测试ZabbixMCPSimple"""
    try:
        from zabbix_mcp_simple import ZabbixMCPSimple

        print('[INFO] 创建ZabbixMCPSimple...')
        client = ZabbixMCPSimple(auto_start_server=True)

        print('[INFO] 测试连接...')
        if await client.test_connection():
            print('[OK] MCP连接成功')

            print('[INFO] 测试获取API版本...')
            try:
                result = await client.call_tool("apiinfo_version")
                print(f'[OK] API版本: {result}')
            except Exception as e:
                print(f'[FAIL] API版本获取失败: {e}')

            print('[INFO] 测试获取主机组...')
            try:
                result = await client.call_tool("hostgroup_get", {
                    "output": ["groupid", "name"],
                    "limit": 3
                })
                print(f'[OK] 获取到主机组:')
                for group in result:
                    print(f'   - {group["name"]} (ID: {group["groupid"]})')
            except Exception as e:
                print(f'[FAIL] 主机组获取失败: {e}')

            print('[INFO] 测试辅助方法...')
            try:
                group_id = await client.get_hostgroup_id('Linux servers')
                if group_id:
                    print(f'[OK] Linux servers主机组ID: {group_id}')
                else:
                    print('[FAIL] 未找到Linux servers主机组')
            except Exception as e:
                print(f'[FAIL] 辅助方法测试失败: {e}')

            await client.disconnect()
            print('[OK] 断开连接成功')

        else:
            print('[FAIL] MCP连接失败')

    except Exception as e:
        print(f'[FAIL] 测试失败: {e}')
        import traceback
        traceback.print_exc()

async def test_zabbix_agent_with_mcp():
    """测试使用MCP的ZabbixAgent"""
    try:
        from zabbix_agent import ZabbixAgent

        print('\n[INFO] 创建ZabbixAgent (使用MCP)...')
        agent = ZabbixAgent(auto_start_server=True)

        print('[INFO] 获取工具列表...')
        tools = await agent.get_tools()
        print(f'[OK] 获取到工具:')
        for tool in tools:
            print(f'   - {tool.name}: {tool.description}')

        print('[INFO] 测试获取主机组信息...')
        try:
            result = await agent.get_hostgroup_info_tool.ainvoke({
                "hostgroup_name": None,  # 获取所有主机组
                "include_hosts": False,
                "include_items": False
            })
            print(f'[OK] 主机组信息获取成功')
            print(f'   结果类型: {type(result)}')

        except Exception as e:
            print(f'[FAIL] 主机组信息获取失败: {e}')

        # 断开连接
        await agent.api_client.disconnect()
        print('[OK] Agent断开连接成功')

    except Exception as e:
        print(f'[FAIL] ZabbixAgent测试失败: {e}')
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("[TEST] Zabbix MCP客户端最终测试")
    print("=" * 60)

    # 测试MCP客户端
    await test_zabbix_mcp_simple()

    print("\n" + "=" * 60)

    # 测试ZabbixAgent
    await test_zabbix_agent_with_mcp()

    print("\n" + "=" * 60)
    print("[INFO] 测试总结:")
    print("[OK] ZabbixMCPSimple: 使用官方MCP SDK")
    print("[OK] ZabbixAgent: 高阶工具集成，MCP通信")
    print("[OK] 架构完整: MCP服务器 → MCP客户端 → ZabbixAgent")
    print("\n[OK] MCP架构验证完成！")

if __name__ == "__main__":
    asyncio.run(main())