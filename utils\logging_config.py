# -*- coding: utf-8 -*-

"""
File: logging_config.py
Author: HuangJun
Date: 2025/6/17

日志配置模块，提供统一的日志配置和工具日志记录功能
"""

import os
import logging
import logging.config
import functools
import inspect
import time
from typing import Any, Callable, Dict, Optional, TypeVar, cast

# 定义日志格式
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
DEFAULT_LOG_LEVEL = logging.INFO

# 创建日志目录
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs")
os.makedirs(LOG_DIR, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=DEFAULT_LOG_LEVEL,
    format=LOG_FORMAT,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(LOG_DIR, "ops_agent.log"), encoding="utf-8"),
    ],
)

# 获取根日志记录器
logger = logging.getLogger("ops_agent")

# 类型变量定义
F = TypeVar("F", bound=Callable[..., Any])

def log_tool_execution(tool_name: Optional[str] = None) -> Callable[[F], F]:
    """
    装饰器：记录工具执行的日志
    
    Args:
        tool_name: 工具名称，如果为None则使用函数名
        
    Returns:
        装饰后的函数
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            # 获取函数名和参数
            fn_name = tool_name or func.__name__
            module_name = inspect.getmodule(func).__name__ if inspect.getmodule(func) else "unknown"
            
            # 过滤掉敏感参数
            safe_kwargs = {k: ("*****" if k.lower() in ["password", "secret", "key", "token"] else v) 
                          for k, v in kwargs.items()}
            
            # 记录开始执行的日志
            start_time = time.time()
            logger.info(f"工具执行开始 [{module_name}.{fn_name}] - 参数: {safe_kwargs}")
            
            try:
                # 执行函数
                result = await func(*args, **kwargs)
                
                # 记录执行成功的日志
                execution_time = time.time() - start_time
                logger.info(f"工具执行成功 [{module_name}.{fn_name}] - 耗时: {execution_time:.2f}秒")
                
                return result
            except Exception as e:
                # 记录执行失败的日志
                execution_time = time.time() - start_time
                logger.error(f"工具执行失败 [{module_name}.{fn_name}] - 耗时: {execution_time:.2f}秒 - 错误: {str(e)}", exc_info=True)
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            # 获取函数名和参数
            fn_name = tool_name or func.__name__
            module_name = inspect.getmodule(func).__name__ if inspect.getmodule(func) else "unknown"
            
            # 过滤掉敏感参数
            safe_kwargs = {k: ("*****" if k.lower() in ["password", "secret", "key", "token"] else v) 
                          for k, v in kwargs.items()}
            
            # 记录开始执行的日志
            start_time = time.time()
            logger.info(f"工具执行开始 [{module_name}.{fn_name}] - 参数: {safe_kwargs}")
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录执行成功的日志
                execution_time = time.time() - start_time
                logger.info(f"工具执行成功 [{module_name}.{fn_name}] - 耗时: {execution_time:.2f}秒")
                
                return result
            except Exception as e:
                # 记录执行失败的日志
                execution_time = time.time() - start_time
                logger.error(f"工具执行失败 [{module_name}.{fn_name}] - 耗时: {execution_time:.2f}秒 - 错误: {str(e)}", exc_info=True)
                raise
        
        # 根据函数类型返回相应的包装器
        if inspect.iscoroutinefunction(func):
            return cast(F, async_wrapper)
        else:
            return cast(F, sync_wrapper)
    
    return decorator