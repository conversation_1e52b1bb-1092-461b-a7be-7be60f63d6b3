# -*- coding: utf-8 -*-

"""
File: main.py
Author: HuangJun
Date: 2025/6/17
"""

import asyncio
import logging
from agents.tencent.tencent_email_agent import TencentEmailAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """腾讯企业邮箱管理工具主函数"""
    try:
        agent = TencentEmailAgent()
        print("欢迎使用腾讯企业邮箱管理工具")
        print("请输入您的指令（例如：'创建张三的邮箱账号'），输入 'exit' 退出程序")
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n请输入指令: ").strip()
                
                if user_input.lower() == 'exit':
                    print("感谢使用，再见！")
                    break
                    
                if not user_input:
                    continue
                
                # 执行指令
                result = await agent.invoke(user_input)
                
                # 显示执行结果
                if result.status == "success":
                    print(f"\n执行成功！")
                    print(f"执行结果: {result.result}")
                else:
                    print(f"\n执行失败！")
                    print(f"失败原因: {result.failed_reason}")
                    
            except KeyboardInterrupt:
                print("\n操作被用户中断，退出程序...")
                break
            except Exception as e:
                logger.error(f"执行指令时发生错误: {str(e)}", exc_info=True)
                print(f"\n发生错误: {str(e)}")
                continue
    except Exception as e:
        logger.critical(f"程序初始化失败: {str(e)}", exc_info=True)
        print(f"程序初始化失败: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断，退出...")
    except Exception as e:
        logger.critical(f"程序运行时发生未处理的异常: {str(e)}", exc_info=True)
        print(f"程序运行时发生未处理的异常: {str(e)}")