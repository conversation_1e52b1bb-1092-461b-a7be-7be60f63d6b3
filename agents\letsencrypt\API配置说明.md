# 证书API配置说明

## 🎯 **概述**

LetsEncrypt代理现在支持从CMDB系统实时获取平台域名映射数据，替代了原来的静态文件方式。这使得平台数据管理更加灵活和实时。

## 🔗 **API接口信息**

### **接口地址**
```
GET https://demo.inhand.online/api/c/compapi/cmdb/model_data_get/
```

### **请求参数**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| bk_app_code | string | 是 | 应用代码：`cmdb` |
| bk_app_secret | string | 是 | 应用密钥：`eee5b34e-fc09-11ea-9e6a-00163e105ceb` |
| model_code | string | 是 | 模型代码：`ZhengShu` |
| username | string | 是 | 用户名：`admin` |
| per_page | int | 否 | 每页数量，默认100 |
| current | int | 否 | 当前页码，默认1 |

### **响应数据结构**
```json
{
    "code": 200,
    "result": true,
    "message": "信息获取成功",
    "data": {
        "current": 1,
        "total": 58,
        "data": [
            {
                "code": 7119,
                "data": {
                    "ZhengShu_DNSServer": "阿里云",
                    "ZhengShu_VISIBLE_NAME": "www-国内论坛-IG Support",
                    "ZhengShu_name": "support.m2mlib.com",
                    "ZhengShu_subjectname": "support.m2mlib.com",
                    "ZhengShu_verfity_type": "txt",
                    "ZhengShu_QianFaJiGou": "Letsencrypt",
                    "ZhengShu_CaiGouFangShi": "Letsencry免费证书",
                    "ZhengShu_price": 0
                }
            }
        ]
    }
}
```

## 📊 **数据字段映射**

| CMDB字段 | 说明 | 示例值 |
|----------|------|--------|
| `ZhengShu_VISIBLE_NAME` | 平台显示名称 | "www-国内论坛-IG Support" |
| `ZhengShu_name` | 证书名称/主域名 | "support.m2mlib.com" |
| `ZhengShu_subjectname` | 证书包含的所有域名 | "support.m2mlib.com, *.support.m2mlib.com" |
| `ZhengShu_DNSServer` | DNS解析商 | "阿里云" / "AWS Route53" / "Cloudflare" |
| `ZhengShu_verfity_type` | 验证类型 | "txt" / "file" |
| `ZhengShu_QianFaJiGou` | 签发机构 | "Letsencrypt" / "Digicert" |
| `ZhengShu_CaiGouFangShi` | 采购方式 | "Letsencry免费证书" |
| `ZhengShu_price` | 价格 | 0 |

## 🔧 **技术实现**

### **API客户端类**
```python
from agents.letsencrypt.certificate_api_client import CertificateAPIClient

# 创建客户端
client = CertificateAPIClient()

# 获取所有证书数据
certificates = client.get_all_certificates()

# 构建平台映射
mapping = client.build_platform_mapping()

# 搜索平台
results = client.search_platforms("研发支撑")
```

### **DNS提供商映射**
```python
dns_provider_map = {
    '阿里云': 'aliyun',
    'AWS Route53': 'aws', 
    'Cloudflare': 'cloudflare'
}
```

## 🚀 **功能特性**

### **1. 实时数据同步**
- 每次代理初始化时自动从API获取最新数据
- 无需手动维护静态文件
- 平台数据变更即时生效

### **2. 智能搜索功能**
- 支持平台名称模糊搜索
- 支持域名关键词搜索
- 支持证书名称搜索

### **3. 容错机制**
- API不可用时自动降级到静态文件
- 网络超时自动重试
- 详细的错误日志记录

### **4. 性能优化**
- 支持分页获取大量数据
- 本地缓存减少重复请求
- 异步处理提升响应速度

## 📈 **当前数据统计**

根据最新API数据：
- **平台总数**: 58个
- **支持自动签发**: 42个平台（仅Letsencrypt + txt验证）
- **不支持自动签发**: 16个平台（其他签发机构或验证方式）
- **DNS提供商分布**（仅支持的平台）:
  - 阿里云DNS: 19个平台
  - AWS Route53: 21个平台
  - 其他: 2个平台

## 🔒 **证书签发限制**

### **支持的证书类型**
- **签发机构**: 仅支持 `Letsencrypt`
- **验证方式**: 仅支持 `txt` (DNS验证)

### **不支持的证书类型**
- **其他签发机构**: Digicert、商业CA等
- **其他验证方式**: file验证、http验证等

### **过滤逻辑**
系统会自动过滤API数据，只有同时满足以下条件的证书才支持自动签发：
```python
issuer == 'Letsencrypt' and verify_type == 'txt'
```

## 🔍 **使用示例**

### **搜索平台**
```python
# 搜索研发支撑相关平台
"搜索研发支撑相关平台"

# 返回结果示例：
# ✅ 找到 2 个支持自动签发的平台：
# 1. **研发支撑-嘉兴s3-online** ✅
#    - 证书名称: ifs.inhand.online
#    - 域名: ifs.inhand.online
#    - DNS提供商: 阿里云
#
# ❌ 找到 1 个不支持自动签发的平台：
# 1. **研发支撑-序列号测试库** ❌
#    - 证书名称: api-test.aws.inhand.online
#    - DNS提供商: 阿里云
#    - 不支持原因: 签发机构为'Digicert'，仅支持Letsencrypt
```

### **智能证书签发**
```python
# 支持的平台 - 自动签发
"为研发支撑嘉兴s3申请证书"
# ✅ 自动匹配到 ifs.inhand.online，使用阿里云DNS，执行证书签发

# 不支持的平台 - 提示原因
"为研发支撑序列号测试库申请证书"
# ❌ 匹配到 api-test.aws.inhand.online，但签发机构为Digicert，不支持自动签发
# 系统会提示：该平台不支持自动签发，原因：签发机构为'Digicert'，仅支持Letsencrypt
```

## ⚙️ **配置说明**

### **API连接配置**
在`certificate_api_client.py`中可以配置：
- `base_url`: API基础地址
- `timeout`: 请求超时时间
- `api_params`: API请求参数

### **备用文件配置**
当API不可用时，系统会自动使用备用文件：
- `platform-domains.md`: 静态平台域名映射文件

## 🔒 **安全考虑**

1. **API密钥管理**: 建议将API密钥配置在环境变量中
2. **HTTPS连接**: 所有API请求使用HTTPS加密
3. **访问控制**: 确保API接口有适当的访问权限控制
4. **数据验证**: 对API返回的数据进行格式验证

## 🐛 **故障排除**

### **常见问题**

1. **API连接失败**
   - 检查网络连接
   - 验证API地址和参数
   - 查看详细错误日志

2. **数据格式错误**
   - 检查API返回的数据结构
   - 验证字段映射是否正确

3. **搜索结果为空**
   - 检查搜索关键词
   - 验证平台数据是否正确加载

### **日志查看**
```bash
# 查看代理日志
tail -f logs/ops_agent.log | grep certificate_api_client
```

---

**通过API集成，LetsEncrypt代理现在具备了动态、实时的平台域名管理能力，大大提升了系统的灵活性和可维护性。**
