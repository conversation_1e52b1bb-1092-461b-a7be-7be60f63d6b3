# -*- coding: utf-8 -*-

"""
File: zabbix_api_client.py
Author: AI Assistant
Date: 2025/7/9

Zabbix API客户端，封装与zabbix-mcp-server的HTTP通信
"""

import os
import json
import requests
import time
import subprocess
import threading
import signal
import atexit
from pathlib import Path
from typing import Dict, List, Optional, Any
from utils.logging_config import logger, log_tool_execution


class ZabbixMCPServerManager:
    """
    Zabbix MCP服务器管理器

    负责自动启动、停止和管理zabbix-mcp-server进程
    """

    def __init__(self, server_url: str = "http://localhost:8000"):
        """
        初始化MCP服务器管理器

        Args:
            server_url: 服务器URL
        """
        self.server_url = server_url
        self.process = None
        self.is_running = False
        self.start_timeout = 30  # 启动超时时间（秒）

        # 获取项目根目录
        current_dir = Path(__file__).parent.parent.parent
        self.mcp_server_dir = current_dir / "mcp-server" / "zabbix-mcp-server"
        self.start_script = self.mcp_server_dir / "scripts" / "start_http_server.py"

        # 注册退出时清理函数
        atexit.register(self.stop_server)

        logger.info(f"MCP服务器管理器初始化，服务器目录: {self.mcp_server_dir}")

    def _check_server_health(self) -> bool:
        """
        检查服务器是否健康运行

        Returns:
            服务器是否正常响应
        """
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def _wait_for_server_start(self) -> bool:
        """
        等待服务器启动完成

        Returns:
            服务器是否成功启动
        """
        logger.info("等待MCP服务器启动...")

        for i in range(self.start_timeout):
            if self._check_server_health():
                logger.info(f"MCP服务器启动成功，耗时 {i+1} 秒")
                return True
            time.sleep(1)

        logger.error(f"MCP服务器启动超时（{self.start_timeout}秒）")
        return False

    def start_server(self) -> bool:
        """
        启动MCP服务器

        Returns:
            是否成功启动
        """
        # 检查是否已经在运行
        if self.is_running and self.process and self.process.poll() is None:
            logger.info("MCP服务器已在运行")
            return True

        # 检查服务器是否已经在其他地方运行
        if self._check_server_health():
            logger.info("检测到MCP服务器已在运行（外部启动）")
            self.is_running = True
            return True

        # 检查启动脚本是否存在
        if not self.start_script.exists():
            logger.error(f"MCP服务器启动脚本不存在: {self.start_script}")
            return False

        try:
            logger.info(f"启动MCP服务器: {self.start_script}")

            # 启动服务器进程
            self.process = subprocess.Popen(
                ["python", str(self.start_script)],
                cwd=str(self.mcp_server_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # 等待服务器启动
            if self._wait_for_server_start():
                self.is_running = True
                logger.info("MCP服务器启动成功")
                return True
            else:
                # 启动失败，清理进程
                if self.process:
                    self.process.terminate()
                    self.process = None
                return False

        except Exception as e:
            logger.error(f"启动MCP服务器失败: {str(e)}")
            return False

    def stop_server(self):
        """
        停止MCP服务器
        """
        if self.process and self.process.poll() is None:
            logger.info("停止MCP服务器...")
            try:
                # 尝试优雅关闭
                self.process.terminate()

                # 等待进程结束
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    logger.warning("MCP服务器未能优雅关闭，强制终止")
                    self.process.kill()
                    self.process.wait()

                logger.info("MCP服务器已停止")
            except Exception as e:
                logger.error(f"停止MCP服务器时发生错误: {str(e)}")
            finally:
                self.process = None
                self.is_running = False

    def ensure_server_running(self) -> bool:
        """
        确保服务器正在运行

        Returns:
            服务器是否正在运行
        """
        if not self.is_running or not self._check_server_health():
            return self.start_server()
        return True


class ZabbixAPIClient:
    """
    Zabbix API客户端类

    封装与zabbix-mcp-server的HTTP通信，提供统一的API调用接口
    """

    def __init__(self, server_url: Optional[str] = None, auto_start_server: bool = True):
        """
        初始化Zabbix API客户端

        Args:
            server_url: zabbix-mcp-server的URL，如果不提供则从环境变量获取
            auto_start_server: 是否自动启动MCP服务器
        """
        self.server_url = server_url or os.getenv("ZABBIX_MCP_SERVER_URL", "http://localhost:8000")
        self.timeout = int(os.getenv("ZABBIX_API_TIMEOUT", "30"))
        self.max_retries = int(os.getenv("ZABBIX_API_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("ZABBIX_API_RETRY_DELAY", "1.0"))
        self.auto_start_server = auto_start_server

        # 初始化MCP服务器管理器
        if self.auto_start_server:
            self.server_manager = ZabbixMCPServerManager(self.server_url)
            # 确保服务器运行
            if not self.server_manager.ensure_server_running():
                logger.warning("无法启动MCP服务器，将尝试连接到现有服务器")
        else:
            self.server_manager = None

        logger.info(f"初始化Zabbix API客户端，服务器URL: {self.server_url}")

    @log_tool_execution(tool_name="_make_request")
    def _make_request(self, tool_name: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发送HTTP请求到zabbix-mcp-server

        Args:
            tool_name: 要调用的工具名称
            params: 工具参数

        Returns:
            API响应数据

        Raises:
            Exception: 请求失败或超时
        """
        if params is None:
            params = {}

        request_data = {
            "tool": tool_name,
            "params": params
        }

        last_exception = None

        for attempt in range(self.max_retries):
            try:
                logger.debug(f"发送请求到 {self.server_url}, 工具: {tool_name}, 尝试: {attempt + 1}")

                response = requests.post(
                    f"{self.server_url}/call_tool",
                    json=request_data,
                    timeout=self.timeout,
                    headers={"Content-Type": "application/json"}
                )

                response.raise_for_status()

                result = response.json()
                logger.debug(f"请求成功，工具: {tool_name}")
                return result

            except requests.exceptions.Timeout as e:
                last_exception = e
                logger.warning(f"请求超时，工具: {tool_name}, 尝试: {attempt + 1}")

            except requests.exceptions.ConnectionError as e:
                last_exception = e
                logger.warning(f"连接错误，工具: {tool_name}, 尝试: {attempt + 1}")

            except requests.exceptions.HTTPError as e:
                last_exception = e
                logger.warning(f"HTTP错误，工具: {tool_name}, 状态码: {e.response.status_code}")

            except Exception as e:
                last_exception = e
                logger.warning(f"未知错误，工具: {tool_name}, 错误: {str(e)}")

            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries - 1:
                time.sleep(self.retry_delay * (attempt + 1))  # 指数退避

        # 所有重试都失败了
        error_msg = f"调用工具 {tool_name} 失败，已重试 {self.max_retries} 次"
        if last_exception:
            error_msg += f"，最后错误: {str(last_exception)}"

        logger.error(error_msg)
        raise Exception(error_msg)

    # ========== 主机和主机组管理 ==========

    @log_tool_execution(tool_name="get_hostgroup_id")
    def get_hostgroup_id(self, hostgroup_name: str) -> Optional[str]:
        """
        根据主机组名称获取主机组ID

        Args:
            hostgroup_name: 主机组名称

        Returns:
            主机组ID，如果未找到返回None
        """
        try:
            result = self._make_request("hostgroup_get", {
                "filter": {"name": hostgroup_name}
            })

            # 解析JSON响应
            if isinstance(result, str):
                data = json.loads(result)
            else:
                data = result

            if data and len(data) > 0:
                return data[0]["groupid"]
            return None

        except Exception as e:
            logger.error(f"获取主机组ID失败: {str(e)}")
            raise

    @log_tool_execution(tool_name="get_host_ids_in_group")
    def get_host_ids_in_group(self, groupid: str) -> List[str]:
        """
        获取指定主机组中的所有主机ID

        Args:
            groupid: 主机组ID

        Returns:
            主机ID列表
        """
        try:
            result = self._make_request("host_get", {
                "groupids": [groupid],
                "output": ["hostid"]
            })

            # 解析JSON响应
            if isinstance(result, str):
                data = json.loads(result)
            else:
                data = result

            return [host["hostid"] for host in data] if data else []

        except Exception as e:
            logger.error(f"获取主机组中的主机ID失败: {str(e)}")
            raise

    # ========== 监控项管理 ==========

    @log_tool_execution(tool_name="get_item_ids")
    def get_item_ids(self, hostids: List[str], item_keys: List[str]) -> Dict[str, List[str]]:
        """
        获取指定主机的指定监控项ID

        Args:
            hostids: 主机ID列表
            item_keys: 监控项key列表，如 ["system.cpu.util", "vm.memory.size[pavailable]"]

        Returns:
            字典，格式为 {item_key: [item_id1, item_id2, ...]}
        """
        try:
            result = self._make_request("item_get", {
                "hostids": hostids,
                "output": ["itemid", "key_"],
                "filter": {"key_": item_keys}
            })

            # 解析JSON响应
            if isinstance(result, str):
                data = json.loads(result)
            else:
                data = result

            # 按key分组
            item_dict = {}
            for item in data:
                key = item["key_"]
                if key not in item_dict:
                    item_dict[key] = []
                item_dict[key].append(item["itemid"])

            return item_dict

        except Exception as e:
            logger.error(f"获取监控项ID失败: {str(e)}")
            raise

    @log_tool_execution(tool_name="get_history")
    def get_history(self, itemids: List[str], time_from: Optional[int] = None,
                   time_till: Optional[int] = None, limit: Optional[int] = None) -> List[Dict]:
        """
        获取监控项的历史数据

        Args:
            itemids: 监控项ID列表
            time_from: 开始时间戳
            time_till: 结束时间戳
            limit: 最大记录数

        Returns:
            历史数据列表
        """
        try:
            params = {
                "itemids": itemids,
                "history": 0,  # 浮点数类型
                "sortfield": "clock",
                "sortorder": "DESC"
            }

            if time_from:
                params["time_from"] = time_from
            if time_till:
                params["time_till"] = time_till
            if limit:
                params["limit"] = limit

            result = self._make_request("history_get", params)

            # 解析JSON响应
            if isinstance(result, str):
                data = json.loads(result)
            else:
                data = result

            return data if data else []

        except Exception as e:
            logger.error(f"获取历史数据失败: {str(e)}")
            raise

    # ========== 问题和事件管理 ==========

    @log_tool_execution(tool_name="get_problems")
    def get_problems(self, hostids: Optional[List[str]] = None,
                    groupids: Optional[List[str]] = None,
                    severities: Optional[List[int]] = None,
                    time_from: Optional[int] = None,
                    time_till: Optional[int] = None,
                    recent: bool = True,
                    limit: Optional[int] = None) -> List[Dict]:
        """
        获取问题列表

        Args:
            hostids: 主机ID列表
            groupids: 主机组ID列表
            severities: 严重级别列表 (0-5)
            time_from: 开始时间戳
            time_till: 结束时间戳
            recent: 是否只获取最近的问题
            limit: 最大记录数

        Returns:
            问题列表
        """
        try:
            params = {
                "output": "extend",
                "recent": recent
            }

            if hostids:
                params["hostids"] = hostids
            if groupids:
                params["groupids"] = groupids
            if severities:
                params["severities"] = severities
            if time_from:
                params["time_from"] = time_from
            if time_till:
                params["time_till"] = time_till
            if limit:
                params["limit"] = limit

            result = self._make_request("problem_get", params)

            # 解析JSON响应
            if isinstance(result, str):
                data = json.loads(result)
            else:
                data = result

            return data if data else []

        except Exception as e:
            logger.error(f"获取问题列表失败: {str(e)}")
            raise

    @log_tool_execution(tool_name="get_triggers")
    def get_triggers(self, hostids: Optional[List[str]] = None,
                    groupids: Optional[List[str]] = None,
                    limit: Optional[int] = None) -> List[Dict]:
        """
        获取触发器列表

        Args:
            hostids: 主机ID列表
            groupids: 主机组ID列表
            limit: 最大记录数

        Returns:
            触发器列表
        """
        try:
            params = {
                "output": "extend"
            }

            if hostids:
                params["hostids"] = hostids
            if groupids:
                params["groupids"] = groupids
            if limit:
                params["limit"] = limit

            result = self._make_request("trigger_get", params)

            # 解析JSON响应
            if isinstance(result, str):
                data = json.loads(result)
            else:
                data = result

            return data if data else []

        except Exception as e:
            logger.error(f"获取触发器列表失败: {str(e)}")
            raise

    # ========== 趋势数据管理 ==========

    @log_tool_execution(tool_name="get_trends")
    def get_trends(self, itemids: List[str], time_from: Optional[int] = None,
                  time_till: Optional[int] = None, limit: Optional[int] = None) -> List[Dict]:
        """
        获取趋势数据

        Args:
            itemids: 监控项ID列表
            time_from: 开始时间戳
            time_till: 结束时间戳
            limit: 最大记录数

        Returns:
            趋势数据列表
        """
        try:
            params = {
                "itemids": itemids
            }

            if time_from:
                params["time_from"] = time_from
            if time_till:
                params["time_till"] = time_till
            if limit:
                params["limit"] = limit

            result = self._make_request("trend_get", params)

            # 解析JSON响应
            if isinstance(result, str):
                data = json.loads(result)
            else:
                data = result

            return data if data else []

        except Exception as e:
            logger.error(f"获取趋势数据失败: {str(e)}")
            raise

    # ========== 系统信息 ==========

    @log_tool_execution(tool_name="get_api_version")
    def get_api_version(self) -> str:
        """
        获取Zabbix API版本信息

        Returns:
            API版本字符串
        """
        try:
            result = self._make_request("apiinfo_version")

            # 解析JSON响应
            if isinstance(result, str):
                return result.strip('"')  # 移除可能的引号
            else:
                return str(result)

        except Exception as e:
            logger.error(f"获取API版本失败: {str(e)}")
            raise

    # ========== 辅助方法 ==========

    def test_connection(self) -> bool:
        """
        测试与zabbix-mcp-server的连接

        Returns:
            连接是否成功
        """
        try:
            # 如果启用了自动启动，先确保服务器运行
            if self.server_manager:
                if not self.server_manager.ensure_server_running():
                    logger.error("无法启动或连接到MCP服务器")
                    return False

            self.get_api_version()
            logger.info("Zabbix MCP服务器连接测试成功")
            return True
        except Exception as e:
            logger.error(f"Zabbix MCP服务器连接测试失败: {str(e)}")

            # 如果连接失败且启用了自动启动，尝试重启服务器
            if self.server_manager:
                logger.info("尝试重启MCP服务器...")
                self.server_manager.stop_server()
                if self.server_manager.start_server():
                    try:
                        self.get_api_version()
                        logger.info("重启后连接测试成功")
                        return True
                    except:
                        pass

            return False