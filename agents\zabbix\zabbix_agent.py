# -*- coding: utf-8 -*-

"""
File: zabbix_agent.py
Author: AI Assistant
Date: 2025/7/9

Zabbix监控分析代理类，提供全面的监控数据获取和智能分析功能。
"""

import os
import asyncio
import nest_asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# 允许嵌套事件循环
nest_asyncio.apply()
from pydantic import BaseModel, Field
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from langchain.tools import tool

from llm.llm import llm
from utils.logging_config import logger, log_tool_execution
from agents.zabbix.zabbix_mcp_client import ZabbixMCPClient
from agents.zabbix.zabbix_schema import (
    GetComprehensiveMetricsSchema,
    GetProblemAnalysisDataSchema,
    GetPerformanceTrendsSchema,
    AnalyzeAndGenerateReportSchema,
    GetHostGroupInfoSchema
)


class ZabbixAgent:
    """
    Zabbix监控分析代理类

    提供全面的Zabbix监控数据获取、分析和报告生成功能，包括：
    - 系统性能指标监控
    - 问题和告警分析
    - 性能趋势分析
    - 智能报告生成
    """

    def __init__(self, auto_start_server: bool = True):
        """
        初始化Zabbix代理

        Args:
            auto_start_server: 是否自动启动MCP服务器
        """
        self.api_client = ZabbixMCPClient()

        # 常用监控项key映射
        self.metric_keys = {
            "cpu": ["system.cpu.util", "system.cpu.load[percpu,avg1]", "system.cpu.load[percpu,avg5]"],
            "memory": ["vm.memory.size[pavailable]", "vm.memory.util", "vm.memory.size[available]"],
            "disk": ["vfs.fs.size[/,pfree]", "vfs.fs.size[/,pused]", "vfs.fs.inode[/,pfree]"],
            "network": ["net.if.in[eth0]", "net.if.out[eth0]", "net.if.in[eth0,bytes]", "net.if.out[eth0,bytes]"],
            "system": ["system.uptime", "proc.num[,,run]", "system.users.num"]
        }

        logger.info("Zabbix代理初始化完成")

    def _get_time_range(self, hours: int) -> tuple:
        """
        获取时间范围的时间戳

        Args:
            hours: 小时数

        Returns:
            (time_from, time_till) 时间戳元组
        """
        now = datetime.now()
        time_till = int(now.timestamp())
        time_from = int((now - timedelta(hours=hours)).timestamp())
        return time_from, time_till

    def _get_time_range_days(self, days: int) -> tuple:
        """
        获取天数范围的时间戳

        Args:
            days: 天数

        Returns:
            (time_from, time_till) 时间戳元组
        """
        now = datetime.now()
        time_till = int(now.timestamp())
        time_from = int((now - timedelta(days=days)).timestamp())
        return time_from, time_till

    @log_tool_execution(tool_name="get_comprehensive_metrics_tool")
    async def get_comprehensive_metrics_tool(self):
        """
        获取综合监控指标工具
        """
        @tool(args_schema=GetComprehensiveMetricsSchema)
        async def get_comprehensive_metrics(
            hostgroup_name: str,
            time_range_hours: Optional[int] = 24,
            include_metrics: Optional[List[str]] = None,
            limit_per_metric: Optional[int] = 100
        ) -> Dict:
            """
            获取指定主机组的综合监控指标数据

            Args:
                hostgroup_name: 主机组名称
                time_range_hours: 时间范围（小时）
                include_metrics: 要包含的指标类型列表
                limit_per_metric: 每个指标的最大数据点数

            Returns:
                包含所有监控指标的综合数据
            """
            try:
                logger.info(f"开始获取主机组 {hostgroup_name} 的综合监控指标")

                # 设置默认指标类型
                if include_metrics is None:
                    include_metrics = ["cpu", "memory", "disk", "network"]

                # 获取主机组ID
                groupid = asyncio.run(self.api_client.get_hostgroup_id(hostgroup_name))
                if not groupid:
                    return {
                        "success": False,
                        "message": f"未找到主机组: {hostgroup_name}",
                        "data": {}
                    }

                # 获取主机组中的主机ID
                hostids = asyncio.run(self.api_client.get_host_ids_in_group(groupid))
                if not hostids:
                    return {
                        "success": False,
                        "message": f"主机组 {hostgroup_name} 中没有主机",
                        "data": {}
                    }

                # 获取时间范围
                time_from, time_till = self._get_time_range(time_range_hours)

                # 收集所有需要的监控项key
                all_keys = []
                for metric_type in include_metrics:
                    if metric_type in self.metric_keys:
                        all_keys.extend(self.metric_keys[metric_type])

                # 获取监控项ID
                item_dict = asyncio.run(self.api_client.get_item_ids(hostids, all_keys))

                # 获取历史数据
                metrics_data = {
                    "hostgroup_name": hostgroup_name,
                    "time_range": {
                        "from": time_from,
                        "till": time_till,
                        "hours": time_range_hours
                    },
                    "hosts_count": len(hostids),
                    "metrics": {}
                }

                for metric_type in include_metrics:
                    metrics_data["metrics"][metric_type] = {}

                    for key in self.metric_keys.get(metric_type, []):
                        if key in item_dict:
                            itemids = item_dict[key]
                            history_data = asyncio.run(self.api_client.get_history(
                                itemids, time_from, time_till, limit_per_metric
                            ))
                            metrics_data["metrics"][metric_type][key] = history_data

                logger.info(f"成功获取主机组 {hostgroup_name} 的监控指标，包含 {len(include_metrics)} 类指标")

                return {
                    "success": True,
                    "message": f"成功获取主机组 {hostgroup_name} 的综合监控指标",
                    "data": metrics_data
                }

            except Exception as e:
                logger.error(f"获取综合监控指标失败: {str(e)}", exc_info=True)
                return {
                    "success": False,
                    "message": f"获取监控指标失败: {str(e)}",
                    "data": {}
                }

        return get_comprehensive_metrics

    @log_tool_execution(tool_name="get_problem_analysis_data_tool")
    async def get_problem_analysis_data_tool(self):
        """
        获取问题分析数据工具
        """
        @tool(args_schema=GetProblemAnalysisDataSchema)
        async def get_problem_analysis_data(
            hostgroup_name: Optional[str] = None,
            severity_levels: Optional[List[int]] = None,
            time_range_hours: Optional[int] = 24,
            include_recent_only: Optional[bool] = True,
            limit: Optional[int] = 50
        ) -> Dict:
            """
            获取问题分析数据，包括当前问题、历史事件等

            Args:
                hostgroup_name: 主机组名称
                severity_levels: 问题严重级别列表
                time_range_hours: 时间范围（小时）
                include_recent_only: 是否只包含最近的问题
                limit: 最大问题数量

            Returns:
                包含问题分析数据的字典
            """
            try:
                logger.info(f"开始获取问题分析数据，主机组: {hostgroup_name}")

                # 设置默认严重级别
                if severity_levels is None:
                    severity_levels = [3, 4, 5]  # 一般严重、严重、灾难

                # 获取时间范围
                time_from, time_till = self._get_time_range(time_range_hours)

                # 获取主机组ID（如果指定了主机组）
                groupids = None
                if hostgroup_name:
                    groupid = asyncio.run(self.api_client.get_hostgroup_id(hostgroup_name))
                    if groupid:
                        groupids = [groupid]
                    else:
                        return {
                            "success": False,
                            "message": f"未找到主机组: {hostgroup_name}",
                            "data": {}
                        }

                # 获取问题数据
                problems = asyncio.run(self.api_client.get_problems(
                    groupids=groupids,
                    severities=severity_levels,
                    time_from=time_from,
                    time_till=time_till,
                    recent=include_recent_only,
                    limit=limit
                ))

                # 获取触发器信息
                triggers = asyncio.run(self.api_client.get_triggers(
                    groupids=groupids,
                    limit=limit
                ))

                problem_data = {
                    "hostgroup_name": hostgroup_name,
                    "time_range": {
                        "from": time_from,
                        "till": time_till,
                        "hours": time_range_hours
                    },
                    "severity_levels": severity_levels,
                    "problems": problems,
                    "triggers": triggers,
                    "summary": {
                        "total_problems": len(problems),
                        "severity_distribution": {}
                    }
                }

                # 统计严重级别分布
                for problem in problems:
                    severity = problem.get("severity", "0")
                    if severity not in problem_data["summary"]["severity_distribution"]:
                        problem_data["summary"]["severity_distribution"][severity] = 0
                    problem_data["summary"]["severity_distribution"][severity] += 1

                logger.info(f"成功获取问题分析数据，共 {len(problems)} 个问题")

                return {
                    "success": True,
                    "message": f"成功获取问题分析数据，共 {len(problems)} 个问题",
                    "data": problem_data
                }

            except Exception as e:
                logger.error(f"获取问题分析数据失败: {str(e)}", exc_info=True)
                return {
                    "success": False,
                    "message": f"获取问题分析数据失败: {str(e)}",
                    "data": {}
                }

        return get_problem_analysis_data

    @log_tool_execution(tool_name="get_performance_trends_tool")
    async def get_performance_trends_tool(self):
        """
        获取性能趋势数据工具
        """
        @tool(args_schema=GetPerformanceTrendsSchema)
        async def get_performance_trends(
            hostgroup_name: str,
            metric_types: Optional[List[str]] = None,
            time_range_days: Optional[int] = 7,
            trend_analysis: Optional[bool] = True
        ) -> Dict:
            """
            获取性能趋势数据，用于长期趋势分析

            Args:
                hostgroup_name: 主机组名称
                metric_types: 要分析趋势的指标类型
                time_range_days: 时间范围（天）
                trend_analysis: 是否进行趋势分析

            Returns:
                包含趋势数据的字典
            """
            try:
                logger.info(f"开始获取主机组 {hostgroup_name} 的性能趋势数据")

                # 设置默认指标类型
                if metric_types is None:
                    metric_types = ["cpu", "memory"]

                # 获取主机组ID
                groupid = asyncio.run(self.api_client.get_hostgroup_id(hostgroup_name))
                if not groupid:
                    return {
                        "success": False,
                        "message": f"未找到主机组: {hostgroup_name}",
                        "data": {}
                    }

                # 获取主机组中的主机ID
                hostids = asyncio.run(self.api_client.get_host_ids_in_group(groupid))
                if not hostids:
                    return {
                        "success": False,
                        "message": f"主机组 {hostgroup_name} 中没有主机",
                        "data": {}
                    }

                # 获取时间范围
                time_from, time_till = self._get_time_range_days(time_range_days)

                # 收集需要的监控项key
                all_keys = []
                for metric_type in metric_types:
                    if metric_type in self.metric_keys:
                        all_keys.extend(self.metric_keys[metric_type])

                # 获取监控项ID
                item_dict = asyncio.run(self.api_client.get_item_ids(hostids, all_keys))

                # 获取趋势数据
                trends_data = {
                    "hostgroup_name": hostgroup_name,
                    "time_range": {
                        "from": time_from,
                        "till": time_till,
                        "days": time_range_days
                    },
                    "hosts_count": len(hostids),
                    "trends": {}
                }

                for metric_type in metric_types:
                    trends_data["trends"][metric_type] = {}

                    for key in self.metric_keys.get(metric_type, []):
                        if key in item_dict:
                            itemids = item_dict[key]
                            trend_data = asyncio.run(self.api_client.get_trends(
                                itemids, time_from, time_till
                            ))
                            trends_data["trends"][metric_type][key] = trend_data

                logger.info(f"成功获取主机组 {hostgroup_name} 的趋势数据，包含 {len(metric_types)} 类指标")

                return {
                    "success": True,
                    "message": f"成功获取主机组 {hostgroup_name} 的性能趋势数据",
                    "data": trends_data
                }

            except Exception as e:
                logger.error(f"获取性能趋势数据失败: {str(e)}", exc_info=True)
                return {
                    "success": False,
                    "message": f"获取性能趋势数据失败: {str(e)}",
                    "data": {}
                }

        return get_performance_trends

    @log_tool_execution(tool_name="analyze_and_generate_report_tool")
    async def analyze_and_generate_report_tool(self):
        """
        分析数据并生成报告工具
        """
        @tool(args_schema=AnalyzeAndGenerateReportSchema)
        async def analyze_and_generate_report(
            metrics_data_json: str,
            problems_data_json: Optional[str] = None,
            trends_data_json: Optional[str] = None,
            user_query: str = "",
            report_type: Optional[str] = "comprehensive",
            include_recommendations: Optional[bool] = True
        ) -> Dict:
            """
            使用AI分析监控数据并生成专业报告

            Args:
                metrics_data_json: 监控指标数据的JSON字符串
                problems_data_json: 问题数据的JSON字符串（可选）
                trends_data_json: 趋势数据的JSON字符串（可选）
                user_query: 用户的原始查询
                report_type: 报告类型
                include_recommendations: 是否包含优化建议

            Returns:
                包含分析报告的字典
            """
            try:
                logger.info(f"开始生成 {report_type} 类型的分析报告")

                # 构建分析提示词
                analysis_prompt = f"""
作为Zabbix监控分析专家，请基于以下数据生成专业的{report_type}分析报告：

用户查询：{user_query}

监控指标数据：
{metrics_data_json}
"""

                if problems_data_json:
                    analysis_prompt += f"\n问题数据：\n{problems_data_json}\n"

                if trends_data_json:
                    analysis_prompt += f"\n趋势数据：\n{trends_data_json}\n"

                analysis_prompt += f"""
请生成一份结构化的分析报告，包含：
1. 总体概览
2. 详细分析（CPU、内存、磁盘、网络等）
3. 问题与告警分析
4. 趋势预测
5. {'优化建议' if include_recommendations else ''}

报告要求：
- 基于实际数据进行分析
- 提供具体的数值和百分比
- 识别异常模式和潜在风险
- 使用专业的监控术语
- 格式清晰，便于阅读
"""

                # 调用LLM生成报告
                response = await llm.ainvoke([
                    SystemMessage("你是一位专业的Zabbix监控分析专家和SRE工程师。"),
                    HumanMessage(analysis_prompt)
                ])

                report_content = response.content

                logger.info(f"成功生成 {report_type} 分析报告")

                return {
                    "success": True,
                    "message": f"成功生成 {report_type} 分析报告",
                    "report": report_content,
                    "report_type": report_type,
                    "generated_at": datetime.now().isoformat()
                }

            except Exception as e:
                logger.error(f"生成分析报告失败: {str(e)}", exc_info=True)
                return {
                    "success": False,
                    "message": f"生成分析报告失败: {str(e)}",
                    "report": "",
                    "report_type": report_type
                }

        return analyze_and_generate_report

    @log_tool_execution(tool_name="get_hostgroup_info_tool")
    async def get_hostgroup_info_tool(self):
        """
        获取主机组信息工具
        """
        @tool(args_schema=GetHostGroupInfoSchema)
        async def get_hostgroup_info(
            hostgroup_name: Optional[str] = None,
            include_hosts: Optional[bool] = True,
            include_items: Optional[bool] = False
        ) -> Dict:
            """
            获取主机组基础信息

            Args:
                hostgroup_name: 主机组名称，如果不指定则获取所有主机组
                include_hosts: 是否包含主机详细信息
                include_items: 是否包含监控项信息

            Returns:
                主机组信息字典
            """
            try:
                logger.info(f"开始获取主机组信息: {hostgroup_name}")

                if hostgroup_name:
                    # 获取指定主机组
                    groupid = asyncio.run(self.api_client.get_hostgroup_id(hostgroup_name))
                    if not groupid:
                        return {
                            "success": False,
                            "message": f"未找到主机组: {hostgroup_name}",
                            "data": {}
                        }

                    hostgroup_data = {
                        "hostgroup_name": hostgroup_name,
                        "groupid": groupid
                    }

                    if include_hosts:
                        hostids = asyncio.run(self.api_client.get_host_ids_in_group(groupid))
                        hostgroup_data["hosts_count"] = len(hostids)
                        hostgroup_data["hostids"] = hostids

                else:
                    # 获取所有主机组（这里需要调用API获取）
                    hostgroup_data = {
                        "message": "获取所有主机组功能待实现"
                    }

                logger.info(f"成功获取主机组信息: {hostgroup_name}")

                return {
                    "success": True,
                    "message": f"成功获取主机组信息",
                    "data": hostgroup_data
                }

            except Exception as e:
                logger.error(f"获取主机组信息失败: {str(e)}", exc_info=True)
                return {
                    "success": False,
                    "message": f"获取主机组信息失败: {str(e)}",
                    "data": {}
                }

        return get_hostgroup_info

    @log_tool_execution(tool_name="get_tools")
    async def get_tools(self) -> List:
        """
        获取所有可用工具的列表

        Returns:
            工具列表
        """
        tools = [
            await self.get_comprehensive_metrics_tool(),
            await self.get_problem_analysis_data_tool(),
            await self.get_performance_trends_tool(),
            await self.analyze_and_generate_report_tool(),
            await self.get_hostgroup_info_tool(),
        ]
        logger.info(f"已加载Zabbix工具列表: {[tool.name for tool in tools]}")
        return tools

    @log_tool_execution(tool_name="invoke")
    async def invoke(self, task: str) -> Any:
        """
        使用 LangChain 的 agent_executor 模式调用工具

        Args:
            task: 用户任务描述

        Returns:
            agent_executor 的执行结果

        Raises:
            Exception: 调用失败
        """
        class ResultModel(BaseModel):
            task: str = Field(..., description="任务执行内容")
            status: str = Field(..., description="操作结果状态，success | failed")
            result: str = Field(..., description="执行完任务后返回的结果，不更改工具返回的结果描述")
            failed_reason: Optional[str] = Field(None, description="执行失败时的原因，详细描述，如执行了什么操作，返回了什么结果")

        logger.info(f"开始处理Zabbix任务: {task}")
        tools = await self.get_tools()

        # 从zabbix_prompt.md文件中读取系统提示词
        prompt_file_path = os.path.join(os.path.dirname(__file__), 'zabbix_prompt.md')
        with open(prompt_file_path, 'r', encoding='utf-8') as f:
            system_prompt = f.read().strip()

        # 创建 agent_executor
        logger.info("创建Zabbix agent_executor")
        agent_executor = create_react_agent(
            llm.bind_tools(tools, parallel_tool_calls=False),
            tools=tools,
            response_format=ResultModel
        )

        # 执行 agent
        logger.info("开始执行Zabbix agent")
        try:
            response = await agent_executor.ainvoke(
                input={
                    "messages": [
                        SystemMessage(system_prompt),
                        HumanMessage(task),
                    ]
                },
                config=RunnableConfig(
                    recursion_limit=50,  # 增加递归限制，允许更复杂的分析任务
                ),
            )

            structured_response = response.get("structured_response")
            logger.info(f"Zabbix任务执行完成，状态: {structured_response.status}")
            return structured_response
        except Exception as e:
            logger.error(f"执行Zabbix agent时发生错误: {str(e)}", exc_info=True)
            # 创建一个错误响应
            error_response = ResultModel(
                task=task,
                status="failed",
                result="Zabbix任务执行失败",
                failed_reason=f"执行过程中发生错误: {str(e)}"
            )
            return error_response