#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单MCP测试 - 基于官方SDK示例
"""

import asyncio
from pathlib import Path
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_simple_mcp():
    """简单MCP连接测试"""
    try:
        # MCP服务器路径
        mcp_server_dir = Path(__file__).parent / "mcp-server" / "zabbix-mcp-server"

        # 配置服务器参数
        server_params = StdioServerParameters(
            command="uv",
            args=["run", "python", "src/zabbix_mcp_server.py"],
            cwd=str(mcp_server_dir)
        )

        print("🔧 启动MCP服务器...")

        # 使用官方SDK的方式连接
        async with stdio_client(server_params) as (read_stream, write_stream):
            print("✅ MCP服务器启动成功")

            async with ClientSession(read_stream, write_stream) as session:
                print("🔗 创建客户端会话...")

                # 初始化会话
                await session.initialize()
                print("✅ 会话初始化成功")

                # 列出可用工具
                tools_result = await session.list_tools()
                print(f"📊 可用工具数量: {len(tools_result.tools)}")

                for tool in tools_result.tools[:3]:  # 只显示前3个
                    print(f"   - {tool.name}: {tool.description}")

                # 测试调用一个简单工具
                try:
                    result = await session.call_tool("apiinfo_version", {})
                    print("✅ API版本调用成功")
                    print(f"   结果: {result}")
                except Exception as e:
                    print(f"⚠️  API版本调用失败: {e}")

                print("✅ 测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_mcp())