#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCP STDIO客户端

通过STDIO与MCP服务器进行通信的客户端实现。
支持启动、停止和调用MCP服务器的工具。
"""

import json
import subprocess
import threading
import time
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import queue
import uuid

logger = logging.getLogger(__name__)


class MCPSTDIOClient:
    """MCP STDIO客户端"""

    def __init__(self, server_command: List[str], server_cwd: Optional[str] = None):
        """
        初始化MCP STDIO客户端

        Args:
            server_command: MCP服务器启动命令
            server_cwd: 服务器工作目录
        """
        self.server_command = server_command
        self.server_cwd = server_cwd
        self.process: Optional[subprocess.Popen] = None
        self.response_queue = queue.Queue()
        self.pending_requests: Dict[str, queue.Queue] = {}
        self.reader_thread: Optional[threading.Thread] = None
        self.is_running = False

    def start_server(self) -> bool:
        """启动MCP服务器"""
        try:
            logger.info(f"启动MCP服务器: {' '.join(self.server_command)}")

            self.process = subprocess.Popen(
                self.server_command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.server_cwd,
                bufsize=0  # 无缓冲
            )

            # 启动读取线程
            self.reader_thread = threading.Thread(target=self._read_responses, daemon=True)
            self.reader_thread.start()

            self.is_running = True

            # 发送初始化请求
            init_result = self._send_request({
                "jsonrpc": "2.0",
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "ZabbixAgent",
                        "version": "1.0.0"
                    }
                }
            })

            if init_result and "result" in init_result:
                logger.info("MCP服务器初始化成功")

                # 发送initialized通知
                self._send_notification({
                    "jsonrpc": "2.0",
                    "method": "notifications/initialized"
                })

                return True
            else:
                logger.error(f"MCP服务器初始化失败: {init_result}")
                return False

        except Exception as e:
            logger.error(f"启动MCP服务器失败: {e}")
            return False

    def stop_server(self):
        """停止MCP服务器"""
        try:
            self.is_running = False

            if self.process:
                self.process.terminate()
                self.process.wait(timeout=5)
                logger.info("MCP服务器已停止")
        except Exception as e:
            logger.error(f"停止MCP服务器失败: {e}")
            if self.process:
                self.process.kill()

    def _read_responses(self):
        """读取服务器响应的线程函数"""
        try:
            while self.is_running and self.process and self.process.stdout:
                line = self.process.stdout.readline()
                if not line:
                    break

                line = line.strip()
                if not line:
                    continue

                try:
                    response = json.loads(line)
                    self._handle_response(response)
                except json.JSONDecodeError as e:
                    logger.warning(f"解析响应失败: {line}, 错误: {e}")

        except Exception as e:
            logger.error(f"读取响应线程错误: {e}")

    def _handle_response(self, response: Dict[str, Any]):
        """处理服务器响应"""
        request_id = response.get("id")

        if request_id and request_id in self.pending_requests:
            # 这是对特定请求的响应
            self.pending_requests[request_id].put(response)
        else:
            # 这是通知或其他响应
            self.response_queue.put(response)

    def _send_request(self, request: Dict[str, Any], timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """发送请求并等待响应"""
        if not self.is_running or not self.process:
            return None

        # 生成请求ID
        request_id = str(uuid.uuid4())
        request["id"] = request_id

        # 创建响应队列
        response_queue = queue.Queue()
        self.pending_requests[request_id] = response_queue

        try:
            # 发送请求
            request_line = json.dumps(request) + "\n"
            self.process.stdin.write(request_line)
            self.process.stdin.flush()

            # 等待响应
            try:
                response = response_queue.get(timeout=timeout)
                return response
            except queue.Empty:
                logger.error(f"请求超时: {request_id}")
                return None

        except Exception as e:
            logger.error(f"发送请求失败: {e}")
            return None
        finally:
            # 清理
            if request_id in self.pending_requests:
                del self.pending_requests[request_id]

    def _send_notification(self, notification: Dict[str, Any]):
        """发送通知（无需响应）"""
        if not self.is_running or not self.process:
            return

        try:
            notification_line = json.dumps(notification) + "\n"
            self.process.stdin.write(notification_line)
            self.process.stdin.flush()
        except Exception as e:
            logger.error(f"发送通知失败: {e}")

    def list_tools(self) -> Optional[List[Dict[str, Any]]]:
        """获取可用工具列表"""
        response = self._send_request({
            "jsonrpc": "2.0",
            "method": "tools/list"
        })

        if response and "result" in response:
            return response["result"].get("tools", [])
        return None

    def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """调用工具"""
        if arguments is None:
            arguments = {}

        response = self._send_request({
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": arguments
            }
        })

        if response and "result" in response:
            return response["result"]
        elif response and "error" in response:
            logger.error(f"工具调用错误: {response['error']}")
            return {"error": response["error"]}

        return None

    def is_healthy(self) -> bool:
        """检查服务器是否健康"""
        if not self.is_running or not self.process:
            return False

        # 检查进程是否还在运行
        if self.process.poll() is not None:
            return False

        # 尝试调用一个简单的工具来测试连接
        try:
            tools = self.list_tools()
            return tools is not None
        except Exception:
            return False