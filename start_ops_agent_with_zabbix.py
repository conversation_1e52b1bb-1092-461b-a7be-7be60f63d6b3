#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OPS Agent with Zabbix 启动脚本

这个脚本会自动启动zabbix-mcp-server并运行ops-agent，
提供完整的Zabbix监控分析功能。

使用方法:
    python start_ops_agent_with_zabbix.py

环境配置:
    1. 复制 mcp-server/zabbix-mcp-server/config/.env.example 为 .env
    2. 配置 ZABBIX_URL 和认证信息
    3. 运行此脚本
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")

    # 检查.env文件
    env_file = Path("mcp-server/zabbix-mcp-server/config/.env")
    env_example = Path("mcp-server/zabbix-mcp-server/config/.env.example")

    if not env_file.exists():
        if env_example.exists():
            print(f"⚠️  未找到配置文件: {env_file}")
            print(f"📝 请复制 {env_example} 为 {env_file} 并配置Zabbix连接信息")
            print("\n配置步骤:")
            print("1. 复制配置文件:")
            print(f"   copy \"{env_example}\" \"{env_file}\"")
            print("2. 编辑配置文件，设置:")
            print("   - ZABBIX_URL: Zabbix服务器地址")
            print("   - ZABBIX_TOKEN 或 ZABBIX_USER/ZABBIX_PASSWORD: 认证信息")
            return False
        else:
            print(f"❌ 配置文件模板不存在: {env_example}")
            return False

    print(f"✅ 找到配置文件: {env_file}")

    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
        print("✅ 环境变量加载成功")
    except ImportError:
        print("⚠️  python-dotenv 未安装，将使用系统环境变量")
    except Exception as e:
        print(f"⚠️  加载环境变量失败: {e}")

    # 检查必需的环境变量
    zabbix_url = os.getenv("ZABBIX_URL")
    if not zabbix_url:
        print("❌ 未配置 ZABBIX_URL")
        return False

    print(f"✅ Zabbix URL: {zabbix_url}")

    # 检查认证配置
    token = os.getenv("ZABBIX_TOKEN")
    user = os.getenv("ZABBIX_USER")
    password = os.getenv("ZABBIX_PASSWORD")

    if token:
        print("✅ 使用 API Token 认证")
    elif user and password:
        print(f"✅ 使用用户名/密码认证: {user}")
    else:
        print("❌ 未配置认证信息（ZABBIX_TOKEN 或 ZABBIX_USER/ZABBIX_PASSWORD）")
        return False

    return True


async def main():
    """主函数"""
    print("🚀 启动 OPS Agent with Zabbix")
    print("=" * 50)

    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请配置后重试")
        return 1

    print("\n🔧 初始化 ZabbixAgent...")

    try:
        # 导入并初始化OpsAgent
        from ops_agent import OpsAgent

        print("✅ OpsAgent 导入成功")

        # 创建OpsAgent实例（这会自动启动zabbix-mcp-server）
        ops = OpsAgent()
        print("✅ OpsAgent 初始化成功")

        # 测试Zabbix功能
        print("\n🧪 测试 Zabbix 功能...")
        tools = await ops.get_tools()
        zabbix_tool_found = any(tool.name == "call_zabbix" for tool in tools)

        if zabbix_tool_found:
            print("✅ Zabbix 工具加载成功")
        else:
            print("❌ Zabbix 工具未找到")
            return 1

        print("\n" + "=" * 50)
        print("🎉 启动成功！")
        print("\n📝 使用说明:")
        print("   现在您可以使用以下类型的查询:")
        print("   - '分析Web服务器主机组的性能状况'")
        print("   - '获取数据库服务器组最近24小时的监控报告'")
        print("   - '检查Linux服务器组的CPU和内存使用情况'")
        print("   - '生成应用服务器组的容量规划报告'")
        print("\n💡 提示:")
        print("   - zabbix-mcp-server 已自动启动")
        print("   - 输入 'exit' 退出程序")
        print("   - 程序退出时会自动停止 zabbix-mcp-server")
        print("=" * 50)

        # 启动交互式会话
        await ops.run_interactive()

        return 0

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保安装了所有依赖包")
        return 1

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)