#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能Zabbix代理

基于MCP (Model Context Protocol) 和 ChatAI 的Zabbix监控代理，
通过AI智能分析用户任务，自动选择合适的MCP方法进行数据获取和分析。
"""

import json
from typing import Dict, List, Optional, Any
from openai import AsyncOpenAI

from utils.logging_config import logger
from .zabbix_mcp_client import ZabbixMCPClient


class ZabbixAgentAI:
    """
    智能Zabbix代理
    
    使用ChatAI来智能分析用户任务，自动选择和调用合适的MCP方法
    """
    
    def __init__(self):
        """初始化智能Zabbix代理"""
        self.mcp_client = ZabbixMCPClient()
        self.openai_client = AsyncOpenAI()
        self.available_tools = []
        
    async def process_task(self, task: str) -> Dict[str, Any]:
        """
        处理用户任务
        
        Args:
            task: 用户任务描述
            
        Returns:
            处理结果
        """
        try:
            logger.info(f"开始处理Zabbix任务: {task}")
            
            # 步骤1: 获取MCP可用工具
            await self._get_available_tools()
            
            # 步骤2: 使用AI分析任务并生成执行计划
            execution_plan = await self._analyze_task_with_ai(task)
            
            # 步骤3: 执行AI生成的计划
            result = await self._execute_plan(execution_plan, task)
            
            return {
                "success": True,
                "message": result.get("message", "任务执行完成"),
                "data": result.get("data", {})
            }
            
        except Exception as e:
            logger.error(f"处理Zabbix任务失败: {e}")
            return {
                "success": False,
                "message": f"处理任务失败: {str(e)}",
                "data": {}
            }
    
    async def _get_available_tools(self) -> None:
        """获取MCP可用工具列表"""
        try:
            logger.info("获取zabbix-mcp服务器的可用工具")
            self.available_tools = await self.mcp_client.get_available_tools()
            logger.info(f"可用的MCP工具: {[tool.get('name', '') for tool in self.available_tools]}")
        except Exception as e:
            logger.warning(f"获取MCP工具列表失败: {e}")
            # 使用默认工具列表
            self.available_tools = [
                {"name": "hostgroup_get", "description": "获取主机组信息"},
                {"name": "host_get", "description": "获取主机信息"},
                {"name": "item_get", "description": "获取监控项信息"},
                {"name": "history_get", "description": "获取历史监控数据"},
                {"name": "problem_get", "description": "获取问题和告警信息"},
                {"name": "trigger_get", "description": "获取触发器信息"},
                {"name": "trend_get", "description": "获取趋势数据"}
            ]
    
    async def _analyze_task_with_ai(self, task: str) -> Dict[str, Any]:
        """
        使用AI分析用户任务并生成执行计划
        
        Args:
            task: 用户任务描述
            
        Returns:
            执行计划
        """
        try:
            logger.info("使用AI分析用户任务")
            
            # 构建AI提示词
            system_prompt = self._build_system_prompt()
            user_prompt = f"""
用户任务: {task}

请分析这个任务，并生成一个详细的执行计划。返回JSON格式，包含：
1. task_type: 任务类型（如：host_monitoring, hostgroup_analysis, problem_check等）
2. steps: 执行步骤列表，每个步骤包含：
   - action: 要执行的MCP方法名
   - params: 方法参数
   - description: 步骤描述
3. expected_output: 期望的输出格式

示例：
{{
    "task_type": "host_monitoring",
    "steps": [
        {{
            "action": "host_get",
            "params": {{"filter": {{"host": ["主机名"]}}}},
            "description": "查找指定主机"
        }},
        {{
            "action": "item_get",
            "params": {{"hostids": ["主机ID"], "search": {{"key_": "监控项关键词"}}}},
            "description": "获取相关监控项"
        }},
        {{
            "action": "history_get",
            "params": {{"itemids": ["监控项ID"], "limit": 100}},
            "description": "获取历史数据"
        }}
    ],
    "expected_output": "详细的监控数据报告"
}}
"""
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1
            )
            
            # 解析AI响应
            ai_response = response.choices[0].message.content
            logger.info(f"AI分析结果: {ai_response}")
            
            # 尝试解析JSON
            try:
                execution_plan = json.loads(ai_response)
                return execution_plan
            except json.JSONDecodeError:
                # 如果不是有效JSON，尝试提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
                if json_match:
                    execution_plan = json.loads(json_match.group())
                    return execution_plan
                else:
                    raise ValueError("AI返回的不是有效的JSON格式")
                    
        except Exception as e:
            logger.error(f"AI分析任务失败: {e}")
            # 返回默认计划
            return self._get_default_plan(task)
    
    def _build_system_prompt(self) -> str:
        """构建AI系统提示词"""
        tools_info = "\n".join([
            f"- {tool.get('name', '')}: {tool.get('description', '')}"
            for tool in self.available_tools
        ])
        
        return f"""你是一个专业的Zabbix监控分析专家。你的任务是分析用户的监控需求，并生成详细的执行计划。

可用的MCP工具：
{tools_info}

你需要：
1. 理解用户的具体需求（主机监控、主机组分析、问题排查等）
2. 选择合适的MCP工具组合
3. 设计合理的执行步骤顺序
4. 生成标准的JSON格式执行计划

注意事项：
- 优先使用最相关的工具
- 考虑数据依赖关系（如先获取主机ID，再获取监控项）
- 参数要符合Zabbix API规范
- 返回的JSON必须格式正确
"""
    
    def _get_default_plan(self, task: str) -> Dict[str, Any]:
        """获取默认执行计划"""
        _ = task  # 暂未使用
        return {
            "task_type": "general_monitoring",
            "steps": [
                {
                    "action": "hostgroup_get",
                    "params": {"output": "extend"},
                    "description": "获取所有主机组"
                }
            ],
            "expected_output": "基础监控信息"
        }
    
    async def _execute_plan(self, plan: Dict[str, Any], original_task: str) -> Dict[str, Any]:
        """
        执行AI生成的计划
        
        Args:
            plan: 执行计划
            original_task: 原始任务描述
            
        Returns:
            执行结果
        """
        try:
            logger.info(f"开始执行计划: {plan.get('task_type', '未知类型')}")
            
            results = {}
            context = {}  # 用于在步骤间传递数据
            
            # 执行每个步骤
            for i, step in enumerate(plan.get("steps", [])):
                action = step.get("action")
                params = step.get("params", {})
                description = step.get("description", "")
                
                logger.info(f"执行步骤 {i+1}: {description}")
                
                # 处理参数中的变量替换
                processed_params = self._process_params(params, context)
                
                # 调用MCP方法
                step_result = await self.mcp_client.call_tool(action, processed_params)
                
                # 保存结果到上下文
                context[f"step_{i+1}_result"] = step_result
                results[f"step_{i+1}"] = {
                    "action": action,
                    "description": description,
                    "result": step_result
                }
                
                logger.info(f"步骤 {i+1} 完成")
            
            # 生成最终报告
            final_report = await self._generate_final_report(results, plan, original_task)
            
            return {
                "message": final_report,
                "data": results
            }
            
        except Exception as e:
            logger.error(f"执行计划失败: {e}")
            return {
                "message": f"执行失败: {str(e)}",
                "data": {}
            }
    
    def _process_params(self, params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理参数中的变量替换"""
        _ = context  # 暂未使用
        # 这里可以实现参数中的变量替换逻辑
        # 例如：将 "${step_1_result.hostid}" 替换为实际的主机ID
        return params
    
    async def _generate_final_report(self, results: Dict[str, Any], plan: Dict[str, Any], original_task: str) -> str:
        """生成最终报告"""
        try:
            logger.info("生成最终报告")
            
            # 使用AI生成专业报告
            system_prompt = """你是一个专业的Zabbix监控报告生成专家。根据执行结果生成清晰、专业的监控报告。"""
            
            user_prompt = f"""
原始任务: {original_task}
执行计划: {json.dumps(plan, ensure_ascii=False, indent=2)}
执行结果: {json.dumps(results, ensure_ascii=False, indent=2)}

请生成一份专业的监控分析报告，包含：
1. 任务概述
2. 关键发现
3. 详细数据
4. 建议和结论
"""
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3
            )
            
            final_report = response.choices[0].message.content
            logger.info("最终报告生成完成")
            return final_report

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            # 如果AI生成报告失败，返回基础报告
            return self._generate_basic_report(results, original_task)

    def _generate_basic_report(self, results: Dict[str, Any], original_task: str) -> str:
        """生成基础报告（当AI报告生成失败时使用）"""
        lines = []
        lines.append(f"# 任务执行报告")
        lines.append(f"**原始任务**: {original_task}")
        lines.append("")

        lines.append("## 执行步骤")
        for step_key, step_data in results.items():
            action = step_data.get("action", "未知")
            description = step_data.get("description", "")
            result = step_data.get("result", {})

            lines.append(f"### {step_key}: {action}")
            lines.append(f"**描述**: {description}")

            # 简单展示结果
            if isinstance(result, list) and result:
                lines.append(f"**结果**: 获取到 {len(result)} 条数据")
                if len(result) <= 5:
                    for i, item in enumerate(result):
                        lines.append(f"  {i+1}. {str(item)[:100]}...")
            elif isinstance(result, dict):
                lines.append(f"**结果**: {str(result)[:200]}...")
            else:
                lines.append(f"**结果**: {str(result)[:100]}...")

            lines.append("")

        lines.append("## 总结")
        lines.append("任务执行完成，详细数据请查看上述步骤结果。")

        return "\n".join(lines)
