# OPS Agent - 统一运维代理

一个基于AI的统一运维代理系统，通过自然语言指令自动化处理各种运维操作。

## 🚀 功能特性

### 🔐 SSL证书管理
- **自动化证书签发**: 支持Let's Encrypt证书自动签发
- **多DNS提供商**: AWS Route53、阿里云DNS、Cloudflare DNS
- **智能平台匹配**: 基于97个企业平台的智能域名匹配
- **自然语言交互**: 支持"研发支撑"、"gitlab"等模糊平台名称
- **智能路由**: 自动识别域名并选择对应的DNS提供商
- **证书类型**: 单域名、多域名、通配符证书
- **密钥类型**: RSA、ECDSA
- **证书监控**: 在线证书状态检查

### 👥 用户管理
- **Azure EntraID**: 用户创建、删除、权限管理
- **自动化流程**: 入职、离职自动化处理
- **组织架构**: 部门管理、角色分配

### 📧 邮箱管理
- **腾讯企业邮箱**: 邮箱开通、管理
- **自动化配置**: 与用户管理联动

### 🧪 测试自动化
- **ParseCases**: 将自然语言描述的测试用例转换为标准化的YAML格式
- **Cases**: 执行标准化的测试用例并生成测试报告

## 🏗️ 系统架构

```
OPS Agent
├── ops_agent.py              # 主入口，统一代理
├── agents/                   # 各功能代理
│   ├── azure/               # Azure EntraID代理
│   ├── tencent/             # 腾讯邮箱代理
│   └── letsencrypt/         # SSL证书代理
├── data/                    # 数据目录
│   └── certificates/        # 证书输出文件
├── letsencrypt/             # Certbot工作目录
├── llm/                     # LLM配置
├── utils/                   # 工具类
└── config/                  # 配置文件
```

## 目录

1. [快速开始](#快速开始)
2. [项目获取和环境安装](#项目获取和环境安装)
    - [安装 uv 包管理器](#1-安装-uv-包管理器)
    - [使用 uv 创建虚拟环境](#2-使用-uv-创建虚拟环境)
    - [同步项目依赖](#3-同步项目依赖)
    - [环境变量配置](#4-环境变量配置)
    - [运行 Python 脚本](#5-运行-python-脚本)
3. [代理模块](#代理模块)
    - [Azure EntraID 代理](#azure-entraid-代理)
    - [腾讯企业邮箱代理](#腾讯企业邮箱代理)
    - [LetsEncrypt SSL证书代理](#letsencrypt-ssl证书代理)
4. [统一代理入口](#统一代理入口)
5. [ParseCases 类](#parsecases-类)
    - [功能介绍](#功能介绍)
    - [测试环境与拓扑图说明](#测试环境与拓扑图说明)
    - [参数说明](#参数说明)
    - [使用示例](#使用示例)
    - [转换后的YAML文件示例及字段说明](#parsecases-转换后的-yaml-文件示例及字段说明)
6. [Cases 类](#cases-类)
    - [环境准备与搭建](#环境准备与搭建)
    - [功能介绍](#功能介绍-1)
    - [参数说明](#参数说明-1)
    - [使用示例](#使用示例-1)
    - [测试用例运行机制与任务编写规范](#测试用例运行机制与任务编写规范)
    - [页面配置任务编写指南](#页面配置任务编写指南)
    - [平台工具使用指南](#平台工具使用指南)
    - [信息获取与上下文传递](#信息获取与上下文传递)
    - [验证任务编写指南](#验证任务编写指南)
    - [条件性完成操作与断言](#条件性完成操作与断言)
    - [设备抓包](#设备抓包)
    - [临时邮箱使用指南](#临时邮箱使用指南)
    - [TCP和UDP数据收发任务编写指南](#TCP和UDP数据收发任务编写指南)
7. [高级用法](#高级用法)
    - [自定义提示词](#自定义提示词)
    - [报告生成](#报告生成)
    - [文件匹配规则示例](#文件匹配规则示例)

## 🛠️ 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone https://gitlab.inhand.design/ops/ops-agent.git
cd ops-agent

# 安装依赖
uv sync

# 安装系统依赖（SSL证书功能）
pip install certbot certbot-dns-route53
```

### 2. 配置环境变量

创建 `.env` 文件：

```bash
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# Azure EntraID配置
AZURE_TENANT_ID=your_tenant_id
AZURE_CLIENT_ID=your_client_id
AZURE_CLIENT_SECRET=your_client_secret

# 腾讯企业邮箱配置
TENCENT_CORP_ID=your_corp_id
TENCENT_CORP_SECRET=your_corp_secret

# AWS Route53配置
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# 阿里云DNS配置
ALICLOUD_ACCESS_KEY=your_alicloud_access_key
ALICLOUD_SECRET_KEY=your_alicloud_secret_key

# Cloudflare DNS配置
CLOUDFLARE_EMAIL=your_cloudflare_email
CLOUDFLARE_API_KEY=your_cloudflare_api_key
# 或者使用API Token（推荐）
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# Let's Encrypt配置
LETSENCRYPT_EMAIL=<EMAIL>
LETSENCRYPT_STAGING=false
```

### 3. 域名托管说明

**重要**：不同域名使用不同的DNS提供商：

| 域名后缀 | DNS提供商 | 用途 | 示例 |
|----------|-----------|------|------|
| `*.inhand.design` | AWS Route53 | Route53证书签发 | `files.inhand.design` |
| `*.inhand.online` | 阿里云DNS | 阿里云证书签发 | `test.inhand.online` |
| `*.inhand.com` | Cloudflare DNS | Cloudflare证书签发 | `resources.inhand.com` |

**使用建议**：
- 申请 `*.inhand.design` 域名的证书时，使用 `AWS Route53`
- 申请 `*.inhand.online` 域名的证书时，使用 `阿里云解析`
- 申请 `*.inhand.com` 域名的证书时，使用 `Cloudflare DNS`

### 3. 运行系统

```bash
# 交互式模式
python ops_agent.py

# 或者直接调用
python -c "
import asyncio
from ops_agent import OpsAgent

async def main():
    agent = OpsAgent()
    result = await agent.invoke('检查www.baidu.com的证书状态')
    print(result)

asyncio.run(main())
"
```

## 📝 使用示例

### SSL证书管理

#### 智能平台匹配（推荐）
```python
# 模糊平台名称 - 自动匹配企业平台
await agent.invoke("为研发支撑平台申请证书")    # 列出所有研发支撑相关平台供选择
await agent.invoke("给gitlab申请证书")          # 自动匹配到gitlab.inhand.design
await agent.invoke("为nezha海外申请证书")       # 自动匹配到*.inhandcloud.com
await agent.invoke("给poweris国内申请证书")     # 自动匹配到poweris.inhand.online
await agent.invoke("为贩卖机平台申请证书")      # 列出所有贩卖机相关平台
```

#### 传统域名签发
```python
# AWS Route53证书签发（*.inhand.design 域名）
await agent.invoke("为example.inhand.design申请证书，使用AWS Route53")
await agent.invoke("为files.inhand.design申请证书，使用AWS Route53，密钥类型ECDSA")

# 阿里云DNS证书签发（*.inhand.online 域名）
await agent.invoke("为test.inhand.online申请证书，使用阿里云解析，密钥类型RSA")
await agent.invoke("为grafana.inhand.online申请证书，使用阿里云DNS")

# Cloudflare DNS证书签发（*.inhand.com 域名）
await agent.invoke("为resources.inhand.com申请证书，使用Cloudflare DNS，密钥类型RSA")
await agent.invoke("为api.inhand.com申请证书，使用Cloudflare，首选证书链ISRG Root X1")

# 证书状态检查
await agent.invoke("检查www.baidu.com的证书状态")
await agent.invoke("检查github.com的SSL证书信息")
```

### 用户管理

```python
# 用户入职（自动创建Azure用户+企业邮箱）
await agent.invoke("张三入职，研发部，邮箱**********************，手机号15882201868")

# 用户离职（自动禁用用户+移除组）
await agent.invoke("张三离职")
await agent.invoke("<EMAIL>.cn离职")

# 单独的Azure用户管理
await agent.invoke("azure 创建用户 <EMAIL>，显示名李四")
```

### 邮箱管理

```python
# 创建企业邮箱
await agent.invoke("mail 创建邮箱账号 <EMAIL>，姓名王五")

# 为现有用户开通邮箱
await agent.invoke("为张三开通企业邮箱")
```

## 简介

ops-agent 是一个基于大模型的企业级自动化运维框架，提供多种运维自动化功能：

### 🎯 核心特性
- **AI驱动**：使用GPT-4进行自然语言理解和处理
- **模块化设计**：每个代理独立运行，可单独使用或组合使用
- **企业级**：支持Azure、腾讯云、AWS等主流企业平台
- **自动化流程**：入职/离职、证书管理等复杂流程一键完成

## 项目获取和环境安装

在继续操作之前，请先获取 TestBotAgent 项目的源代码。项目仓库地址如下：  
https://gitlab.inhand.design/ops/ops-agent.git

> **注意：推荐使用 Python 3.11环境**  > 如果你需要使用 Python 3.11，请确保已正确安装对应版本，并在创建虚拟环境时指定 Python 3.11 的解释器路径。

> **重要：本项目的多个组件需要管理员权限才能正常运行。请确保在管理员模式下运行所有相关程序。**

### 1. 安装 uv 包管理器

**必须使用 PowerShell（不是 cmd）**

打开 **Windows PowerShell**（右键以管理员身份运行），执行：

```powershell
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```
安装完成后，关闭并重新打开Powershell, 检查是否成功：

```powershell
uv --version
```

出现版本号说明安装成功。

---

### 2. 使用 uv 创建虚拟环境

进入你的项目目录，比如：

```powershell
cd C:\Users\<USER>\my_project
```

创建虚拟环境：

```powershell
uv venv
```

激活虚拟环境（Windows系统） 你需要在.venv目录下：

```powershell
.\.venv\Scripts\activate
```

看到前面有 `(.venv)`，说明虚拟环境激活成功。

---

### 3. 同步项目依赖

使用 uv 安装 Python 依赖（例如 `pyyaml`、`langchain`）： 

```powershell
uv sync （第一次执行时使用uv init）
```
---

### 4. 安装langchain 浏览器
```powershell
uv add langchain
```
---

### 4. 环境变量配置
#### 5.1 新建 `.env` 文件

在项目根目录下新建一个 `.env` 文件，用于配置环境变量。  
**注意：.env 文件内容仅供示例，实际值请根据实际环境填写。**

例如，`.env` 文件内容如下：
```text
OPENAI_API_KEY=      openai 的api key
OPENAI_PROXY=        访问openai大模型的代理
LANGFUSE_SECRET_KEY=      开启langfuse 追踪
LANGFUSE_PUBLIC_KEY=      开启langfuse 追踪
LANGFUSE_HOST=           开启langfuse 追踪
```


### 5. 运行 Python 脚本

**必须以管理员身份运行命令提示符或PowerShell**，然后执行你的测试用例生成脚本，例如：

```powershell
python cases.py
```

---

## 代理模块

ops-agent 框架包含多个专业的代理模块，每个代理都专注于特定的运维任务。

### Azure EntraID 代理

Azure EntraID 代理专门处理 Microsoft Azure Active Directory (现在称为 Microsoft Entra ID) 的用户和组管理任务。

#### 功能特性
- ✅ **用户管理**：创建、删除、更新、禁用、启用用户
- ✅ **组管理**：创建、删除组，用户加入/退出组
- ✅ **密码管理**：重置用户密码
- ✅ **离职流程**：自动处理用户离职（禁用账号、移除所有组）
- ✅ **查询功能**：获取用户、组信息

#### 使用示例
```python
from ops_agent import APIAgent

agent = APIAgent()

# 用户入职
result = await agent.invoke("张三入职，邮箱**********************，手机号15888888888，部门研发部")

# 用户离职
result = await agent.invoke("张三离职")

# Azure用户管理
result = await agent.invoke("azure 创建用户 <EMAIL>，显示名李四")
```

### 腾讯企业邮箱代理

腾讯企业邮箱代理专门处理腾讯企业邮箱的账号管理任务。

#### 功能特性
- ✅ **邮箱账号管理**：创建、删除、查询、更新企业邮箱账号
- ✅ **与腾讯企业邮箱API集成**
- ✅ **批量操作支持**

#### 使用示例
```python
from ops_agent import APIAgent

agent = APIAgent()

# 邮箱管理
result = await agent.invoke("mail 创建邮箱账号 <EMAIL>，姓名王五")
```

### LetsEncrypt SSL证书代理

LetsEncrypt SSL证书代理提供全自动的SSL证书管理功能，支持多种DNS提供商。

#### 功能特性
- 🔐 **证书签发**：单域名、多域名、通配符证书签发
- 🔄 **证书管理**：自动续期、撤销、状态查询
- 📦 **证书导出**：PEM、P12、JKS格式导出
- 🌐 **DNS提供商支持**：AWS Route53、阿里云DNS、GoDaddy DNS

#### 环境要求
纯Python实现，无需Docker容器：

```bash
# 安装Python依赖（已包含在项目依赖中）
uv sync

# 可选：安装完整ACME库支持
uv add acme>=2.11.0 boto3>=1.35.0 josepy>=1.14.0
```

#### 使用示例
```python
from ops_agent import APIAgent

agent = APIAgent()

# AWS Route53域名签发
result = await agent.invoke("为 pypi.inhand.design 签发证书，使用AWS Route53验证")

# 阿里云DNS通配符证书
result = await agent.invoke("为 *.inhand.design 签发通配符证书，使用阿里云DNS验证")

# 证书续期
result = await agent.invoke("续期 example.com 的证书")

# 查看证书状态
result = await agent.invoke("查看所有证书状态")

# 导出证书
result = await agent.invoke("导出 example.com 的证书为PEM格式")
```

## 统一代理入口

`ops_agent.py` 提供了统一的代理入口，能够智能路由用户请求到合适的专业代理。

### 智能路由规则
1. **Azure相关**：以"azure"开头的请求 → Azure EntraID代理
2. **邮箱相关**：以"mail"开头的请求 → 腾讯邮箱代理
3. **证书相关**：包含"证书"、"ssl"、"https"、"域名签发"、"letsencrypt"等关键词 → LetsEncrypt代理
4. **入职流程**：包含"xxx入职" → 先Azure创建用户，再创建邮箱
5. **离职流程**：包含"xxx离职" → Azure禁用用户、移除组

### 使用方法
```python
from ops_agent import APIAgent
import asyncio

async def main():
    agent = APIAgent()

    # 自动路由到合适的代理
    result = await agent.invoke("张三入职，邮箱**********************，手机号15888888888，部门研发部")
    print(f"状态: {result.status}")
    print(f"结果: {result.result}")

if __name__ == "__main__":
    asyncio.run(main())
```

---

## ParseCases 类

### 功能介绍

ParseCases 类用于将自然语言描述的测试用例转换为标准化的 YAML 格式，便于后续执行和管理。该类利用大模型将非结构化的测试描述转换为结构化的测试用例。

### 测试环境与拓扑图说明

ParseCases 类在转换测试用例时，需要明确测试环境和设备拓扑结构。系统默认使用 `prompt/device_topology_prompt.md`
文件中定义的环境信息，您也可以通过 `extend_system_prompt_file` 参数提供自定义的环境描述。

#### 默认测试环境

- **被测路由器**：映翰通路由器设备
    - **WAN1口**：已连接办公室局域网，使用静态IP，能访问办公室局域网和外网
    - **蜂窝口**：已插入SIM卡1和SIM卡2，当前工作模式为"仅sim1"
    - **LAN口**：提供本地网络连接

- **终端1**：
    - 是一台windows 的PC端
    - 只有一张网卡且连接到**被测路由器**的LAN2口
    - 已设置静态IP，能正常访问被测路由器
    - 上网流量通过被测路由器优先级最高的上行链路

- **本机**：
    - 是一台windows 的PC机，有两张网卡
    - 一张网卡连接到被测路由器的LAN3口，已设置静态IP，能正常访问被测路由器
    - 一张网卡连接到办公室局域网，已设置静态IP，能访问办公室局域网和外网， 用于邮件收发、运行agent

- **平台**：
    - 是映翰通管理路由器等数通产品的网络管理平台
    - 将设备成功添加到平台后，平台提供远程设备管理、设备监控、设备告警、设备升级、设备终端维护等功能

#### 自定义测试环境

您可以通过以下方式自定义测试环境：

1. **修改默认拓扑文件**：直接编辑 `prompt/device_topology_prompt.md` 文件
2. **提供自定义拓扑文件**：创建自己的拓扑描述文件，并通过 `extend_system_prompt_file` 参数指定

```python
# 使用自定义拓扑文件的示例
parser = ParseCases(
    cases_text=test_case_text,
    file_name="wan2_test",
    suite="network_tests",
    output_dir="./my_test_cases",
    extend_system_prompt_file="./my_custom_topology.md"
)
```

### 参数说明

```python
class ParseCases:
    def __init__(
            self,
            cases_text: str,  # 需要转换的多个用例文案
            file_name="default",  # 转换后的用例文件名，为file_name.yaml
            suite: str = "default",  # 由多个测试用例组成的集合，用于组织和批量执行测试
            **kwargs,  # 其他可选参数
    ):
```

**可选参数 (kwargs)**：

- `output_dir`：转换后的用例保存目录，默认是 `./case`
- `extend_system_prompt_file`：追加的系统提示词文件，默认是 `./parse_cases.md`

### 使用示例

```python
from inhandtest_agent.cases import ParseCases

# 自然语言描述的测试用例
test_case_text = """
测试WAN2接口配置

用例1：添加WAN2接口并使用DHCP上网
1. 登录路由器Web界面
2. 进入网络设置页面
3. 添加WAN2接口，并配置为DHCP
4. 验证WAN2接口状态为up
5. 验证WAN2接口能正常上网

用例2：修改WAN2接口为静态IP
1. 登录路由器Web界面
2. 进入网络设置页面
3. 将WAN2接口修改为静态IP
4. 设置IP地址、子网掩码、网关和DNS
5. 验证WAN2接口状态为up
6. 验证WAN2接口能正常上网
"""

# 创建ParseCases实例并执行转换
parser = ParseCases(
    cases_text=test_case_text,
    file_name="wan2_test",
    suite="network_tests",
    output_dir="./my_test_cases"
)

# 执行转换，生成YAML文件
parser.run()
```

执行后，会在 `./my_test_cases` 目录下生成 `wan2_test.yaml` 文件，包含结构化的测试用例。

### ParseCases 转换后的 YAML 文件示例及字段说明

```yaml
# 所有用例执行前的公共前置条件
pre_each_conditions:
  - task: 添加WAN2口，并配置为DHCP，WAN2口已添加成功，WAN2口状态为up，WAN2口能正常上网

# 测试用例列表
cases:
  - id: TC-WAN2-001                                # 用例ID，通常包含功能模块缩写和序号
    title: 检查WAN2接口能否正常上网（DHCP）          # 用例标题，简要描述测试内容
    goal: 验证WAN2接口添加并配置为DHCP后能否正常访问外网  # 用例目标，详细说明测试目的
    pre_conditions: # 用例特定的前置条件
      - task: WAN2口配置为DHCP后，WAN2口状态为up
    exec_steps: # 执行步骤
      - task: 在终端1上通过被测路由器访问www.baidu.com，能正常访问外网

# 测试套件名称
suite: default
```

#### 顶层字段

1. pre_each_conditions ：

    - 用途：定义所有测试用例执行前需要满足的公共前置条件
    - 格式：任务列表，每个任务使用 task 字段描述
    - 执行时机：在执行任何测试用例之前，这些条件会被首先验证或设置,添加到每个测试用例的前置条件中
2. cases ：

    - 用途：包含所有测试用例的数组
    - 格式：每个测试用例是一个包含多个字段的对象
3. suite ：

    - 用途：指定测试套件名称，用于组织和批量执行测试
    - 格式：字符串
    - 默认值："default"



## Cases 类

### 环境准备与搭建

在执行测试用例之前，需要确保测试环境中的所有设备都处于正常工作状态。环境准备包括设备连接验证、角色配置确认和基础功能检查。

#### 环境设备角色定义

测试环境中的设备角色在 `agents/config.py` 文件中定义，每个角色都有对应的配置参数, 如需要新增可在文件中自行定义：

```python
# agents/config.py 中的设备角色配置
agent_devices = {
    "被测路由器": {
        "model": "ER815",
        "host": "***********",
        "protocol": "https",
        "username": "adm",
        "password": "123456",
        "port": 443,
        "ss_port": 22,
    },
    "终端1": {
        "model": "windows_pc",
        "host_url": "http://************:8000",
    },
    "本机": {
        "model": "local_machine",
    },
    "平台": {
        "model": "star_platform",
        "host": "star.inhandcloud.com",
        "username": "<EMAIL>",
        "password": "123456",
        # 'proxy': True, # 是否使用代理
    },
}
```

#### 环境角色说明

根据 `prompt/device_topology_prompt.md` 文件定义，测试环境中各设备，如需新增需要添加角色描述：

1. 被测路由器 ：映翰通路由器设备

2. 终端1 ：Windows PC终端
   - 需要在终端1上运行 agents/windows_pc/windows_remote_server.py  **必须以管理员身份运行**
   - 运行前需要安装 fastmcp 库
   - 配置脚本中的 host 地址和端口
   - 需要安装 Nmap 工具包:
     1. 从 Nmap 官网(https://nmap.org/download.html)下载最新版本的 Windows 安装包
     2. 运行安装包进行安装，选择完整安装(包含 Npcap 和其他工具)
     3. 将 Nmap 安装目录(默认为 C:\Program Files (x86)\Nmap)添加到系统环境变量 Path 中
     4. 打开命令提示符，输入 `ncat -h` 验证安装和环境变量配置是否成功
   - 原因:终端1需要运行该脚本来提供远程控制服务,以便测试框架能够远程执行终端1上的操作,如网络连通性测试等。该脚本会启动一个HTTP服务器,接收并执行测试框架发送的远程命令。同时 Nmap 工具包中的 ncat 用于执行 TCP/UDP 数据的收发测试。

3. 本机 ：执行测试的本地机器
    - 运行agent的机器，需要有pycharm等IDE，**必须以管理员身份运行pycharm**
    - 拉取项目代码，并按要求执行用例或生成用例
    - 需要安装 Nmap 工具包:
     1. 从 Nmap 官网(https://nmap.org/download.html)下载最新版本的 Windows 安装包
     2. 运行安装包进行安装，选择完整安装(包含 Npcap 和其他工具)
     3. 将 Nmap 安装目录(默认为 C:\Program Files (x86)\Nmap)添加到系统环境变量 Path 中
     4. 打开命令提示符，输入 `ncat -h` 验证安装和环境变量配置是否成功
   

4. 平台 ：小星云管理平台


### 功能介绍


Cases 类用于执行标准化的测试用例，支持批量执行、生成测试报告，并提供丰富的配置选项。

### 参数说明

```python
class Cases:
    def __init__(self, cases_root_path="./case", **kwargs):
```

**主要参数**：

- `cases_root_path`：需要运行的用例根目录，能递归查询根目录下所有符合正则表达式的文件

**可选参数 (kwargs)**：

- `case_filename_regex`：用例文件名正则表达式，默认是 `*.yaml`，遵循 glob 规则
- `extend_device_prompt_filename_regex`：追加到设备运行时的系统提示词文件名正则表达式，默认是 `*device.md`
- `case_prompt_filename_regex`：在每个用例运行时的系统提示词，默认是 `*case.md`
- `allure_results_dir`：生成 allure 报告的路径，默认是 `./allure-results`
- `yaml_report_dir`：YAML 格式测试报告的存储目录，默认是 `./yaml_report`

### 使用示例

```python
import asyncio
from inhandtest_agent.cases import Cases

# 创建Cases实例
cases_runner = Cases(
    cases_root_path="./my_test_cases",
    case_filename_regex="wan2_*.yaml",
    allure_results_dir="./my_allure_results",
    yaml_report_dir="./my_yaml_reports"
)

# 执行测试用例
asyncio.run(cases_runner.run())
```

执行后，会运行 `./my_test_cases` 目录下所有以 `wan2_` 开头、`.yaml` 结尾的测试用例文件，并在指定目录生成测试报告。

### 测试用例运行机制与任务编写规范

### 用例执行流程

#### 完整执行流程

Cases 类在执行测试用例时，遵循以下逻辑流程：

1. **用例文件遍历**：按照指定的目录和文件名正则表达式，顺序遍历每个可运行的用例文件。系统会递归查找符合条件的所有YAML文件，并按照文件路径的顺序依次执行。

2. **用例预处理**：在执行每个用例文件前，系统会根据提供的 case_prompt_filename_regex 参数加载对应的系统提示词，对用例内容进行优化和变更，生成最终的测试套件用例。这一步骤确保用例能够适应当前的测试环境和需求。

3. **用例顺序执行**：系统会按照YAML文件中定义的顺序依次执行每个测试用例。在执行过程中，会自动加载对应的系统提示词（通过 extend_device_prompt_filename_regex追加设备系统提示词以指导各设备角色的行为、extend_windows_prompt_filename_regex 追加windows系统提示词以指导各Windows终端的行为、extend_star_prompt_filename_regex 追加小星云平台提示词以指导各小星云平台的行为）。

4. **任务执行顺序**：对于每个测试用例，系统会按照以下顺序执行任务：
   - 首先执行所有用例共享的前置条件（pre_each_conditions）
   - 然后执行特定用例的前置条件（pre_conditions）
   - 最后执行测试步骤（exec_steps）
   - 每个部分中的任务（task）会按照YAML文件中定义的顺序依次执行。需要注意的是，**每个task的执行是相互独立的**，即当前执行的task无法直接获知上一个task或下一个task的具体内容，只能通过上下文中的output字段传递信息。

5. **错误处理机制**：如果某个步骤执行失败，系统会立即停止当前用例的后续步骤执行，并将该用例标记为失败。这确保了测试的准确性，避免在前提条件不满足的情况下继续执行后续步骤。

6. **清理步骤生成**：无论用例是否执行成功，系统都会分析已执行成功的添加、启用、禁用等操作，自动生成对应的清理步骤。这些清理步骤会被添加到 cleanup_steps 字段中，确保测试环境能够恢复到初始状态。

7. **清理步骤执行**：系统会执行生成的清理步骤，以恢复测试环境。如果清理步骤执行失败，系统会跳过后续的清理步骤，并且该文件中的所有后续用例都将被跳过不再执行，以避免在不确定的环境状态下继续测试。

8. **报告生成**：测试完成后，系统会生成详细的测试报告，包括YAML格式的原始报告和Allure格式的可视化报告。报告中包含每个任务的执行状态、结果、失败原因（如果有）以及使用的工具信息。

9. **session 清理**：在测试完成后，系统会清理所有agent的 sessions，确保测试环境的干净状态。

#### 错误处理与恢复策略

在测试执行过程中，系统采用了严格的错误处理策略，确保测试的可靠性和环境的一致性：

1. **步骤失败处理**：当某个步骤执行失败时，系统会记录失败原因，并跳过当前用例的所有后续步骤。这避免了在前提条件不满足的情况下继续执行，从而产生误导性的测试结果。

2. **重试机制**：对于某些可能因时序问题导致的暂时性失败，系统提供了自动重试机制。执行任务时无需描述。

3. **环境恢复**：通过自动生成和执行清理步骤，系统确保每个测试用例执行后，环境都能恢复到初始状态，为后续测试提供一致的起点。

4. **级联失败处理**：如果清理步骤执行失败，系统会认为环境已处于不确定状态，因此会跳过当前文件中的所有后续用例。这种保守策略确保了测试结果的可靠性。

#### 操作角色指定

在编写task时，需要明确指定操作角色，以便系统知道应该由哪个设备或角色来执行该任务：

1. **默认角色**：如果task中没有明确指定操作角色，系统默认由**被测路由器**执行该任务

2. **明确指定角色**：在task开头指定操作角色，例如：
   ```yaml
   - task: 终端1 能正常访问www.baidu.com
3. 复杂任务的角色指定 ：对于较长且涉及多个角色交互的任务，建议使用方括号明确标识操作角色，例如：

```yaml
- task: [ 终端1 ] 能正常访问www.baidu.com [被测路由器] 启用蜂窝接口 [终端1] 发起tcp的数据包，目标地址为*********** 端口为66， 数据包内容为test
```

#### 页面配置任务编写指南

在编写与Web界面配置相关的任务时，为了确保系统能够正确识别并执行配置操作，建议遵循以下规范：

##### 明确功能模块

任务描述中应明确指出要操作的功能模块名称，这有助于系统准确识别需要调用的工具。主要功能模块包括：

1. **internet**：上行链路配置
    - 蜂窝配置（双卡模式）
    - 蜂窝流量策略配置
    - WAN口配置
    - Wi-Fi配置
    - 上网优先级配置
    - 链路探测配置

2. **local_network**：本地网络配置
    - 添加/编辑本地网络
    - 删除本地网络

3. **wifi**：Wi-Fi网络配置
    - 添加/编辑/删除Wi-Fi

4. **firewall**：防火墙配置
    - 入站规则配置
    - 出站规则配置
    - 端口转发配置
    - 固定地址转换(NAT)配置
    - MAC地址过滤配置
    - 域名过滤配置

5. **policy_based_routing**：策略路由配置
    - 添加/编辑/删除策略路由

6. **traffic_shaping**：流量整形配置
    - 接口带宽配置
    - 整形规则配置

7. **services**：服务配置
    - 接口管理
    - DHCP服务器
    - DNS服务器
    - 固定地址列表
    - 静态路由
    - 动态DNS
    - 透传设置

8. **system**：系统配置
    - 云服务配置
    - 远程访问控制
    - 系统时钟
    - 配置管理
    - 设备告警
    - 定时重启
    - 日志服务器
    - 账户管理
    - 其他设置

9. **VPN相关配置**
    - **ipsec_vpn**：IPSec VPN配置
    - **l2tp_vpn**：L2TP VPN配置
    - **vxlan_vpn**：VXLAN VPN配置
    - **gre_vpn**：GRE VPN配置

10. **认证相关配置**
    - **portal**：Portal认证配置
    - **authentication_802_1x**：802.1x认证配置

11. **升级相关操作**
    - **upgrade**：设备升级
    - **module_upgrade**：模组升级
    - **restore_to_factory**：恢复出厂设置

##### 参数配置规范

在描述配置任务时，应明确指出各参数的名称和值。例如：

```yaml
- task: 配置WAN1接口为静态IP，IP地址为************0，子网掩码为*************，网关为***********
```

或者更结构化的描述方式：

```yaml
- task: [ 被测路由器 ] 配置internet模块的WAN1接口，设置类型为静态IP，参数包括：IP地址(************0)、子网掩码(*************)、网关(***********)、DNS(*******)
```

通过明确指定操作角色以及功能模块和详细的参数配置，可以帮助系统更准确地理解和执行Web界面配置任务。

#### 平台工具使用指南

小星云平台提供了丰富的工具集，按功能分类如下：

##### 设备管理工具

1. 设备基础操作 ：

    - add_device ：添加设备到平台
    - update_device ：更新平台上的设备信息
    - delete_device ：删除平台上的设备
    - get_device_info ：获取设备信息
    - get_device_uplink_info ：获取设备上行链路信息
    - get_device_interface_info: 获取设备接口信息
2. 许可证管理 ：

    - bind_license ：设备绑定许可证
    - unbind_license : 设备解绑许可证
3. 日志管理 ：

    - download_log_online ：下载平台的在线日志

##### 配置管理工具

1. 配置操作 ：
    - get_config ：获取设备配置或组配置
    - clear_config ：清除设备配置
    - send_config ：下发设备配置

##### 分组管理工具

1. 分组操作 ：
    - create_group ：创建分组
    - delete_group ：删除分组
    - move_device_to_group ：设备移入或移出分组
    - find_device_in_group ：在指定分组中查找设备
    - get_group_info ：获取分组信息

##### 组织管理工具

1. 组织操作 ：
    - get_org_info ：获取组织信息

##### 告警管理工具

1. 告警规则操作 ：
    - create_alert_rule ：创建告警规则
    - update_alert_rule ：更新告警规则
    - delete_alert_rule ：删除告警规则
    - get_all_rule ：获取所有告警规则

##### 固件管理工具

1. 固件操作 ：
    - firmware_version ：获取指定产品的固件版本或插件版本

##### 网络管理工具

1. 云连接网络操作 ：

    - add_connector_network ：添加云连接网络
    - update_connector_network ：更新云连接网络
    - delete_connector_network ：删除云连接网络
    - add_device_to_connector ：将设备添加到云连接网络
    - remove_device_from_connector ：将设备从云连接网络中删除
    - add_endpoint_to_connector ：添加终端到云连接网络
    - delete_endpoint_from_connector ：删除云连接网络中的终端
    - add_account_to_connector ：添加账号到云连接网络
    - delete_account_from_connector ：删除云连接网络中的账号
    - download_account_openvpn_config ：下载账号的openvpn配置文件
    - get_connector_network ：获取云连接网络信息
2. SDWAN网络操作 ：

    - add_sd_wan_network ：添加SDWAN网络
    - delete_sd_wan_network ：删除SDWAN网络
    - modify_sd_wan_network ：修改SDWAN网络
    - get_sd_wan_network ：获取SDWAN网络信息
    - get_sd_wan_network_connect_info ：获取SDWAN网络连接信息
    - get_sd_wan_network_tunnel_info ：获取SDWAN网络隧道信息

#### 信息获取与上下文传递

在测试用例执行过程中，经常需要获取某些信息并在后续步骤中使用这些信息。TestBotAgent框架提供了上下文传递机制，允许在不同任务之间传递信息。

- 系统提供了多种获取设备信息的方法
  **信息提取任务示例**：
   ```yaml
   - task: 获取蜂窝网口IP地址
   - task: 保存wan口网关地址
   - task: 查询设备当前运行状态
   ```
- 上下文传递机制   
  当任务执行后，系统会自动将获取的信息存储在 output 字段中，后续任务可以通过上下文引用这些信息：

1. 信息存储 ：

    - 每个task执行后，重要信息会被自动或手动存储在该task的 output 字段中
    - 系统会维护一个上下文环境，包含所有之前任务的输出信息
2. 上下文引用语法 ：

    - 使用： 引用之前步骤的输出定义的字段名称
    - 例如： 引用之前步骤中存储的wan1接口IP地址
3. 上下文引用示例 ：

   ```yaml
   - task: 获取蜂窝网口IP地址
     # 执行后自动存储在output中，例如：{"cellular_ip":"********"}
   - task: 使用ping工具测试从终端1访问蜂窝网口IP地址
     # 自动替换为：使用ping工具测试从终端1访问********
   ``` 

- 最佳实践

1. 明确信息获取意图 ：

    - 在任务描述中明确表明需要获取什么信息
    - 使用动词如"获取"、"查询"、"保存"等表明这是一个信息提取任务
2. 合理命名输出字段 ：

    - 使用有意义的字段名，如 wan1口ip 、 蜂窝状态 等
    - 避免使用过于通用的名称，如 result 、 data 等
3. 验证与获取分离 ：

    - 信息获取任务专注于提取信息并存储
    - 验证任务专注于使用这些信息进行验证
4. 复杂信息处理 ：

    - 对于复杂的信息处理需求，可以使用多个步骤逐步提取和处理
    - 例如，先获取完整配置，再从中提取特定字段

#### 验证任务编写指南

在测试用例中，验证任务是确保系统行为符合预期的关键步骤。以下是编写验证任务时应遵循的规范：

##### 验证任务的基本原则

1. 明确性原则 ：验证任务应明确表达预期结果，避免模糊表述
2. 可测量性原则 ：验证内容应该是可以客观测量和判断的
3. 独立性原则 ：每个验证任务应该独立完成，不依赖其他验证任务的结果
4. 完整性原则 ：验证应覆盖关键的功能点和边界条件

##### 验证任务的语法规范

1. 使用"验证"关键词
   验证任务应以"验证"、"期望"、"确认"等关键词开头，明确表示这是一个验证操作：

```yaml
# 推荐写法
- task: 验证WAN1接口状态为up
- task: 期望设备能正常访问外网
- task: 确认蜂窝网络连接正常

  # 不推荐写法
- task: WAN1接口状态为up  # 缺少验证关键词
- task: 检查一下网络是否正常  # 表述模糊
```

2. 明确验证对象和预期值
   验证任务应明确指出验证的对象、属性和预期值：

```yaml
# 设备状态验证
- task: 期望设备ER815NLDGJ的连接状态为在线
- task: 验证设备ER815NLDGJ的配置同步状态为已同步

# 接口状态验证
- task: 期望WAN1接口的连接状态为connected
- task: 验证蜂窝接口cellular1的信号强度大于-80dBm

# 网络连通性验证
- task: 期望终端1能正常访问www.baidu.com
- task: 验证从本机ping ***********能正常响应

# 配置参数验证
- task: 期望WAN1接口的IP地址为************0
- task: 验证DHCP服务器的地址池范围为************-************0
```

3. 重试机制
   对于可能需要时间生效的验证，系统会自动重试：

```yaml
- task: 下发配置到设备ER815NLDGJ
- task: 期望设备ER815NLDGJ的配置同步状态为已同步
  # 系统会自动重试最多6次，每次间隔15秒
```

4. 验证在约定时间内完成
   在部分场景中需要约定不超过某个时间达到一定的状态：

```yaml
- task: 验证设备的wan1接口在10秒内状态为up
  # 系统会在10秒内完成验证，如果超过10秒仍未完成，任务失败
```

#### 条件性完成操作与断言

在实际测试场景中，经常需要根据当前系统状态来决定执行不同的操作。条件性完成操作允许在任务中定义条件判断逻辑，根据检查结果执行相应的操作。

##### 条件性操作的基本语法

条件性操作使用"当...时则..."的语法结构，明确表达条件和对应的操作：

```yaml

# 基本语法格式
- task: 获取wan口信息，当wan2口不存在时则添加wan2口
- task: 检查设备连接状态，当设备离线时则重启设备
- task: 验证DHCP配置，当DHCP未启用时则启用DHCP服务
```

##### 常见的条件性操作场景

1. 接口管理场景

```yaml

# WAN口管理
- task:
    获取wan口配置信息，当wan2口不存在时则创建wan2口  output: wan_config
- task: 检查wan1口状态，当wan1口为down状态时则启用wan1口# LAN口管理
- task: 查询LAN口配置，当LAN1口IP不是***********时则修改为***********
- task: 获取VLAN配置，当VLAN100不存在时则创建VLAN100
```

2. 设备状态管理

```yaml

# 设备连接状态
- task: 检查设备ER815NLDGJ连接状态，当设备离线时则等待设备上线
- task: 获取设备许可证状态，当许可证过期时则更新许可证# 服务状态管理
- task: 检查DHCP服务状态，当DHCP服务未启动时则启动DHCP服务
- task: 验证防火墙状态，当防火墙未启用时则启用防火墙
```

3. 网络配置场景

```yaml

# 路由配置
- task: 检查默认路由，当默认路由不存在时则添加默认路由
- task: 获取静态路由表，当目标路由不存在时则添加静态路由***********/24# 网络连接
- task: 测试网络连通性，当无法访问外网时则检查DNS配置
- task: 验证VPN连接，当VPN连接失败时则重新建立VPN连接
```

4. 用户和权限管理

```yaml

# 用户管理
- task: 查询用户列表，当admin用户不存在时则创建admin用户
- task: 检查用户权限，当用户权限不足时则提升用户权限# 组织架构
- task: 获取设备组信息，当测试组不存在时则创建测试组
- task: 检查设备归属，当设备不在指定组时则移动设备到目标组
```

##### 条件性断言的编写规范

1. 条件表达清晰
   条件部分应该明确表达判断标准：

```yaml

# 推荐写法 - 条件明确
- task: 获取wan1口信息，当wan1口状态为down时则启用wan1口
- task: 检查设备版本，当固件版本低于v2.1.0时则升级固件
- task: 验证许可证，当许可证剩余天数少于30天时则续期许可证 # 不推荐写法 - 条件模糊
- task: 检查wan1口，如果有问题就修复  # 条件不明确
- task: 看看设备状态，不对就处理一下  # 表述模糊
```

2. 操作具体明确
   操作部分应该具体说明要执行的动作：

```yaml

# 推荐写法 - 操作具体
- task: 获取DHCP配置，当DHCP地址池为空时则配置地址池************-************0
- task: 检查防火墙规则，当端口80规则不存在时则添加允许HTTP访问的规则 # 不推荐写法 - 操作模糊  
- task: 检查DHCP，没配置就配置一下  # 操作不具体- task: 看防火墙，缺规则就加规则  # 操作不明确
```

3. 支持复合条件
   对于复杂场景，支持多个条件的组合：

```yaml

# 多条件判断
- task: 检查网络配置，当wan1口为DHCP模式且获取不到IP时则切换为静态IP模式
- task: 验证设备状态，当设备在线但配置未同步时则重新下发配置
- task: 获取接口信息，当wan1口存在但状态为down时则重启接口
```

#### 设备抓包

在测试过程中，有时需要抓取设备接口的网络数据包来验证网络行为。以下是抓包相关的说明：

##### 抓包规范

1. **接口指定**
   - 必须明确指定要抓包的接口名称，如 "WAN1"、"WAN2"、"蜂窝" 等
   - 一次只能对一个接口进行抓包

2. **过滤表达式**
   - 需要指定过滤表达式来筛选感兴趣的数据包, **目的：需要在过滤表达式中写上明确的抓包内容，判断时只需要判断有无抓包结果即可**
   - 支持标准的 tcpdump 过滤表达式语法
   - 示例：`tcp and port 80`、`icmp`、`host *******` 等

3. **并发限制**
   - 同一设备在同一时间只能运行一个抓包任务
   - 新的抓包任务会自动停止当前正在运行的抓包任务

4. **结果验证**
   - 抓包结果只能验证数据包是否存在
   - 不支持对数据包内容进行详细分析或其他复杂判断

##### 使用示例
- 仅抓包： 在蜂窝口上开启抓包、协议为icmp
- 仅断言抓包内容：查看抓包结果，确认有抓包内容
- 抓包加断言：在蜂窝口上开启抓包、协议为icmp，查看抓包结果，确认有抓包内容

#### 临时邮箱使用指南

在测试过程中，某些场景需要使用临时邮箱来接收验证邮件。
在执行所有的用例前，系统会使用mail.tm服务自动注册一个临时邮箱地址（包含对应的密码）， 所以在使用时只需说明是临时邮箱即可。

##### 使用方法

- **设备配置告警接收** [被测路由器上启用设备告警邮件接收，配置发件服务器地址：smtp.163.com，端口25， 用户名：<EMAIL>， 密码：MCZDEL, 配置接收邮箱地址为临时邮箱地址，保存]
- **邮箱校验内容** [验证临时邮箱中存在主题包含‘登录成功’且内容包含‘登录成功’的邮件， 操作对象必须是本机]

#### TCP和UDP数据收发任务编写指南

在测试过程中，经常需要验证网络连通性和数据传输功能。TestBotAgent支持通过终端设备（如终端1、本机等Windows PC）进行TCP和UDP协议的数据收发测试。

##### 基本语法规范

在编写涉及TCP/UDP数据收发的任务时，需要明确指定操作角色、协议类型、目标地址、端口和数据内容。

##### TCP数据发送任务

```yaml
# 基本语法
- task: [角色名] 发起tcp的数据包，目标地址为[IP地址] 端口为[端口号]，数据包内容为[内容]

# 使用示例
- task: 终端1 发起tcp的数据包，目标地址为*********** 端口为66，数据包内容为test
- task: 本机 发起tcp的数据包，目标地址为************0 端口为8080，数据包内容为hello world
```

##### UDP数据发送任务

```yaml
# 基本语法
- task: [角色名] 发起udp的数据包，目标地址为[IP地址] 端口为[端口号]，数据包内容为[内容]

# 使用示例
- task: 终端1 发起udp的数据包，目标地址为*********** 端口为53，数据包内容为dns query
- task: 本机 发起udp的数据包，目标地址为************0 端口为1234，数据包内容为test message
```

##### TCP数据接收任务

```yaml
# 基本语法
- task: [角色名] 使用tcp协议接收数据，监听端口[端口号]
- task: [角色名] 使用tcp协议接收数据，监听地址[IP地址]端口[端口号]

# 使用示例
- task: 终端1 使用tcp协议接收数据，监听端口1234
- task: 本机 使用tcp协议接收数据，监听地址************端口8080
```

##### UDP数据接收任务

```yaml
# 基本语法
- task: [角色名] 使用udp协议接收数据，监听端口[端口号]
- task: [角色名] 使用udp协议接收数据，监听地址[IP地址]端口[端口号]

# 使用示例
- task: 终端1 使用udp协议接收数据，监听端口5353
- task: 本机 使用udp协议接收数据，监听地址************端口53
```
        

## 高级用法

### 自定义提示词

您可以通过在用例目录中放置特定命名的 Markdown 文件来自定义系统提示词：

1. **设备提示词**：文件名匹配 `*device.md` 的文件会被用作设备相关的系统提示词
2. **用例提示词**：文件名匹配 `*case.md` 的文件会被用于用例优化

系统会从用例文件所在目录开始，向上查找直到根目录，收集所有匹配的提示词文件。

### 报告生成

执行完成后，可以使用以下命令查看 Allure 报告：

```bash
allure serve ./my_allure_results
```

### 文件匹配规则示例

```python
# 执行case目录下的所有以_mail_cases.yaml结尾的文件
Cases("case", case_filename_regex="*_mail_cases.yaml")

# 执行case目录下的test_mail_cases.yaml文件
Cases("case", case_filename_regex="test_mail_cases.yaml")

# 执行case目录下的所有以test_开头，以.yaml结尾的文件
Cases("case", case_filename_regex="test_*.yaml")

# 执行case目录下的所有yaml或yml文件（包括目录嵌套，递归执行）
Cases("case", case_filename_regex="*")

# 执行case目录下的所有在二级目录下的yaml或yml文件（包括目录嵌套，递归执行）
Cases("case", case_filename_regex="*/*")
```

---

## 📚 文档

- [系统架构文档](docs/ARCHITECTURE.md) - 完整的系统架构、功能说明和开发指南
- [LetsEncrypt代理文档](agents/letsencrypt/README.md) - SSL证书管理详细说明
- [智能平台域名匹配](agents/letsencrypt/平台域名匹配功能说明.md) - 97个企业平台的智能匹配功能
- [配置管理](config.py) - 统一配置管理和验证工具

---

通过这个使用手册，您应该能够开始使用 OPS Agent 框架进行自动化运维。如有任何问题，请参考源代码或联系开发团队获取更多支持。

